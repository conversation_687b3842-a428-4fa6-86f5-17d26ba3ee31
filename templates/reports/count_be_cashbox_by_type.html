{% extends 'layout.html' %}
{% load staticfiles bootstrap3 bootstrap_input_groups %}

{% block js %}
    <script src="{% static "core/js/bootstrap3-typeahead.js" %}"></script>
{% endblock %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Создание отчета по количеству КО у СПД, в разрезе классификационных групп моделей КО</h3>
        </div>

        <form method="post" class="form-horizontal" style="margin-top: 30px">
            {% csrf_token %}

            {% bootstrap_field form.unp layout='horizontal' field_class='col-sm-4' label_class='col-lg-1' %}
            {% bootstrap_date_field form.date layout='horizontal' field_class='col-sm-2' label_class='col-lg-1' %}

            <div class="row col-sm-6">
                <div class="col-md-8 pull-right" >
                    <input type="submit" class="btn btn-primary" value="Сформировать отчет"/>
                    <a href="{{ exit_url }}" class="btn btn-default">
                        <span>Выход</span>
                    </a>
                </div>
            </div>
        </form>
    </div>

    <script>
        $(document).ready(function(){
            $('#id_unp').typeahead({
                source: function (data) {
                    var that = this;
                    $.get('{% url 'report_be_suggest' %}', {'search_request': data}, function(result) {
                        that.process(result);
                    });
                },
                property: 'name',
                matcher: function() {return true},
                updater: function(item) {
                    return item['unp']
                }
            });
        });
    </script>
{% endblock %}