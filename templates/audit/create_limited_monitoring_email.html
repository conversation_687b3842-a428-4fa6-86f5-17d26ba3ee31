{% extends 'layout.html' %}
{% load bootstrap3 bootstrap_input_groups static%}
{% block js %}
    <script src="{% static "core/js/bootstrap3-typeahead.js" %}"></script>
{% endblock %}

{% block content %}
    <div class="container">
        <div class="p-3 mb-2 bg-warning text-dark text-center font-weight-bold" style="padding: 10px" id="warning" hidden="hidden">Введите правильный Е-mail.</div>

        <div class="page-header">
            <h3>Создание е-mail ограниченного мониторинга</h3>
        </div>

        <form id="FormForData" method="post" class="form-horizontal">
            {% csrf_token %}

            <div hidden="hidden">
                {% bootstrap_field form.list_user layout='horizontal' field_class='col-sm-6' label_class='col-lg-6' %}
            </div>

            <div class="row">
                <div class="col-sm-8">
                    {% bootstrap_field form.user_name layout='horizontal' field_class='col-sm-6' label_class='col-lg-6' %}
                </div>

                <div id="block_email">
                    <div class="col-sm-8">
                        {% bootstrap_field form.email layout='horizontal' field_class='col-sm-6' label_class='col-lg-6' %}
                    </div>

                    <div class="col-sm-2">
                        <p class="form-control-static">
                            <span class="glyphicon glyphicon-plus" style="color: #428bca; cursor: pointer"
                                  id="add_email"></span>
                        </p>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-8 pull-right">
                        <input type="submit" class="btn btn-primary" value="Сохранить"/>
                        <a href="{% url 'audit_limited_monitoring_email_create' %}" class="btn btn-default">
                            <span>Отменить</span>
                        </a>
                        <a href="{{ exit_url }}" class="btn btn-default">
                            <span>Выход</span>
                        </a>
                    </div>
                </div>
            </div>
        </form>
    </div>

    <script>
        $(function () {
            $('#id_user_name').typeahead({
                source: search_element,
                property: 'fullname',
                matcher: function () {
                    return true
                },
                onselect: function select(value) {
                    $('#id_user_name').val(value['fullname']);
                },
                updater: function (item) {
                    $('#id_list_user').val(item.id)
                }
            });
        });
        $("#add_email").on('click', function () {

            let row = document.createElement("row");
            let set_block = '<div class="col-sm-8">' + '{% bootstrap_field form.email layout="horizontal" field_class="col-sm-6" label_class="col-lg-6" %}' + '</div>' +
                   '<div class="col-sm-2">'+
                       '<p class="form-control-static">'+
                           '<span class="glyphicon glyphicon-minus" style="color: #428bca; cursor: pointer"id="remove_email"></span>'+
                       '</p>'+
                   '</div>'
               ;
            row.addEventListener("click", remove_row_input)
            row.innerHTML = set_block;
            $(row).find('input').val('');
            $('#block_email').append(row);
        });

        function remove_row_input(event) {
            if (event.target.tagName === 'SPAN' && event.target.className === "glyphicon glyphicon-minus") {
                $(this).remove();
            }
        };

        function search_element(query) {
            let that = this
            if (query !== '') {
                $.get('{% url 'audit_user_suggest' %}', {'surname': query}, function (result) {
                    that.process(result);
                });
            }
        };

        function check_email(email) {
            return /.+@.*\..+/.test(email)
        }

        $("form").on("submit", function () {
            $("input[name ='submit']").prop("disabled", true);
            let input_emails = document.querySelectorAll('input[name ="email"]');
            for (let i = 1; i < input_emails.length; i++) {
                if (!input_emails[i].value) {
                    input_emails[i].closest("row").remove()
                    continue
                } else if (!check_email(input_emails[i].value)) {
                    $("#warning").removeAttr('hidden');
                    return false
                }
            }
            return true
        });


    </script>
{% endblock %}