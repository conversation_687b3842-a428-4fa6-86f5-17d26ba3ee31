{% extends 'layout.html' %}
{% load permissions bootstrap3 bootstrap_input_groups %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Редактирование партии SIM</h3>
        </div>
        <form method="post" class="form-horizontal" style="margin-top: 20px">
            {% csrf_token %}
            {% bootstrap_field form.creator_name layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}
            {% bootstrap_field form.status layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}
            {% bootstrap_field form.hardware_count layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}
            {% bootstrap_field form.technical_center layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}

            <div class="row col-sm-6">
                <div class="col-md-8 pull-right" >
                    <input type="submit" class="btn btn-primary" value="Сохранить"/>
                    <a href="{% url 'device_sim_part_edit' form.instance.pk %}" class="btn btn-default">
                        <span>Отменить</span>
                    </a>
                    <a href="{{ exit_url }}" class="btn btn-default">
                        <span>Выход</span>
                    </a>
                    {% ifperms sim_part_delete %}
                        <a href="{% url 'device_sim_part_delete' form.instance.pk %}"
                           onclick="return confirm('Вы действительно хотите удалить?')"
                           class="btn btn-danger pull-right">
                                Удалить
                        </a>
                    {% endifperms %}
                </div>
            </div>

        </form>
    </div>
{% endblock %}