{% extends 'layout.html' %}
{% load staticfiles bootstrap3 bootstrap_input_groups permissions  %}

{% block js %}
    {% if not skno.events %}
        <script src="{% static "core/select2/select2.min.js" %}"></script>
        <script src="{% static "core/select2/select2_locale_ru.js" %}"></script>
        <script src="{% static "core/js/bootstrap3-typeahead.js" %}"></script>
    {% endif %}
{% endblock %}

{% block css %}
    <link href="{% static "core/select2/select2.css" %}" rel="stylesheet" media="screen">
    <link href="{% static "core/select2/select2-bootstrap.css" %}" rel="stylesheet" media="screen">
{% endblock %}

{% block content %}
<div class="container">
    <div class="page-header">
        <h3>Редактирование СКНО</h3>
    </div>

    <div class="row">
        <div class="col-sm-6">
            <form id="skno_form" method="post" class="form-horizontal" autocomplete="off">
                {% csrf_token %}

                {% if skno.events %}
                    <div class="form-group">
                        <label class="col-sm-4 control-label">Заводской номер СКНО</label>
                        <div class="col-sm-4">
                            <p class="form-control-static">{{ skno.skno_hardware.factory_number }}</p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">Номер СКЗИ модуля</label>
                        <div class="col-sm-4">
                            <p class="form-control-static">{{ skno.skzi_hardware.serial_number }}</p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">Код SIM(ICIID) </label>
                        <div class="col-sm-4">
                            <p class="form-control-static">{{ skno.sim_hardware.sim_code }}</p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">Телефонный номер SIM карты</label>
                        <div class="col-sm-4">
                            <p class="form-control-static">{{ skno.sim_hardware.phone_number|default_if_none:'' }}</p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">Учетный номер КО</label>
                        <div class="col-sm-4">
                            <p class="form-control-static">{{ skno.cashbox.account_number }}</p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">Интерфейс подключения</label>
                        <div class="col-sm-4">
                            <p class="form-control-static">{{ skno.cashbox.get_connection_interface_display }}</p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">Тип</label>
                        <div class="col-sm-4">
                            <p class="form-control-static">{{ skno.get_type_display }}</p>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">Исполнитель</label>
                        <div class="col-sm-4"><p class="form-control-static">{{ skno.performer }}</p></div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-4 control-label">Технический центр</label>
                        <div class="col-sm-4">
                            <p class="form-control-static">{{ skno.technical_center }}</p>
                        </div>
                    </div>
                {% else %}
                    {% bootstrap_field skno_form.factory_number layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}
                    {% bootstrap_field skno_form.skzi_module_number layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}
                    {% bootstrap_field skno_form.sim_code layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}
                    {% bootstrap_field skno_form.cashbox layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}
                    {% bootstrap_field skno_form.type layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}
                {% endif %}

                {% bootstrap_field skno_form.test_result layout='horizontal' field_class='col-sm-8' label_class='col-lg-4' %}
                {% bootstrap_field skno_form.note layout='horizontal' field_class='col-sm-8' label_class='col-lg-4' %}
            </form>
        </div>

        <div class="col-sm-6">
            <form id="event_form" method="post" class="form-horizontal" autocomplete="off">
                {% csrf_token %}

                {% if skno.last_event_code == 0 %}
                    {% if not skno.related_request or skno.related_request.status == 2 and skno.related_request.engineer_registrar %}
                        {% ifperms request_skno_equipment %}
                            {% bootstrap_datetime_field event_form.created_datetime layout='horizontal' field_class='col-sm-4' label_class='col-lg-3' %}
                            <button type="submit" class="btn btn-default" name="event" value="equipment">Снаряжено</button>
                        {% endifperms %}
                    {% endif %}
                {% elif skno.last_event_code == 1 %}
                    {% if not skno.analogous_connected_skno or skno.related_request %}
                        {% if not skno.related_request or skno.related_request.status == 2 and skno.related_request.work_order %}
                            {% ifperms request_skno_installation %}
                                {% bootstrap_datetime_field event_form.created_datetime layout='horizontal' field_class='col-sm-4' label_class='col-lg-3' %}
                                <button type="submit" class="btn btn-default" name="event" value="installation">Установить</button>
                            {% endifperms %}
                        {% endif %}
                    {% endif %}
                    {% if not skno.related_request.work_order %}
                        <button type="submit" class="btn btn-default" name="event" value="disassemble">Разукомплектовать</button>
                    {% endif %}
                {% elif skno.last_event_code == 2 or skno.last_event_code == 4 %}
                    {% bootstrap_datetime_field event_form.created_datetime layout='horizontal' field_class='col-sm-4' label_class='col-lg-3' %}

                    {% if not skno.related_request or skno.related_request.status == 2 and skno.related_request.work_order and skno.related_request.engineer_registrar %}
                        <div class="btn-group">
                            <button type="submit" class="btn btn-default" name="event" value="withdrawal0">
                                Снять по причине снятия КО с учета
                            </button>

                            <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                                <span class="sr-only">Toggle Dropdown</span>
                            </button>

                            <ul id="cause_menu" class="dropdown-menu" role="menu">
                                <li><a data-cause="1" name="withdrawal" href="#">Снять по причине перевыпуска ТСОК</a></li>
                                <li><a data-cause="2" name="withdrawal" href="#">Снять по причине ремонта</a></li>
                                <li><a data-cause="3" name="withdrawal" href="#">Снять по причине хищение</a></li>
                            </ul>
                        </div>
                    {% endif %}
{#13.04.2016#}
{#                    <button type="submit" class="btn btn-default" name="event" value="turn_on">Включить</button>#}
{#                {% elif skno.last_event_code == 3 %}#}
{#                    {% bootstrap_datetime_field event_form.created_datetime layout='horizontal' field_class='col-sm-4' label_class='col-lg-3' %}#}
{#                    <button type="submit" class="btn btn-default" name="event" value="turn_off">Выключить</button>#}
                {% endif %}
            </form>

            {% if skno.events %}
                <table class="table table-striped" style="margin-top: 30px">
                    <thead>
                        <tr>
                            <td>Состояние</td>
                            <td>Дата и время</td>
                        </tr>
                    </thead>
                    <tbody>
                        {% for event in skno.events|slice:'::-1' %}
                            <tr>
                                <td>
                                    {{ event.get_event_display }}
                                    {% if event.event == 5 %} ({{ event.get_withdrawal_cause_display }}){% endif %}
                                </td>
                                <td>{{ event.created_datetime|date:'DATETIME_WITH_SECONDS_FORMAT' }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% endif %}

            {% ifperms skno_passport_download %}
                <div class="row col-lg-offset-9" style="margin-top: 40px">
                    <a id="skno_passport" href="#">Паспорт СКНО</a>
                </div>
            {% endifperms %}

            {% ifperms skzi_hardware_equip %}
                {% if not skno.withdrawal_datetime %}
                    <div class="row col-lg-offset-9" style="margin-top: 40px">
                        <a href="{% url 'device_skzi_hardware_equip' skno.skzi_hardware.pk %}">Снарядить СКЗИ</a>
                    </div>
                {% endif %}
            {% endifperms %}

        </div>
    </div>

    <div class="row col-lg-12">
        <div class="col-lg-10 col-lg-offset-2">
            <button type="button" class="btn btn-primary"
                    onclick="$('#skno_form').prop('action', '{{ request.path }}' + '?save=not_redirect').submit();">
                Сохранить и продолжить редактирование
            </button>
            <input type="button" value="Сохранить" class="btn btn-default" onclick="$('#skno_form').submit()"/>
            <a href="{% url 'device_skno_edit' skno.pk %}" class="btn btn-default">
                <span>Отменить</span>
            </a>
            <a href="{{ exit_url }}" class="btn btn-default">
                <span>Выход</span>
            </a>

            <div class="pull-right">
                <a href="{% url 'core_cashbox_events' skno.cashbox.pk %}" class="btn btn-default">События КО</a>
                {% if skno.related_request %}
                    <a href="{{ skno.related_request.get_edit_url }}" class="btn btn-default">
                        Перейти к редактированию заявки
                    </a>
                {% endif %}
            </div>

        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#cause_menu').on('click', '[name=withdrawal]', function() {
            var add_input = '<input type="hidden" name="event" value="withdrawal' + $(this).data('cause') + '">';
            $('#event_form').prepend(add_input).submit();
            return false;
        });

        {% if not skno.events %}
            $('#id_factory_number').typeahead({
                source: function (data) {
                    var that = this;
                    $.get('{% url 'device_skno_hardware_suggest' %}',
                            {'factory_number': data, 'include_skno_pk': {{ skno.skno_hardware.pk }}},
                            function(result) {
                                that.process(result);
                            }
                    );
                },
                property: 'factory_number',
                updater: function(item) {
                    return item['factory_number']
                }
            });

            $('#id_skzi_module_number').typeahead({
                source: function (data) {
                    var that = this;
                    $.get('{% url 'device_skzi_hardware_suggest' %}',
                            {'serial_number': data, 'include_skzi_pk': {{ skno.skzi_hardware.pk }}},
                            function(result) {
                                that.process(result);
                            }
                    );
                },
                property: 'serial_number',
                updater: function(item) {
                    return item['serial_number']
                }
            });

            $('#id_sim_code').typeahead({
                source: function (data) {
                    var that = this;
                    $.get('{% url 'device_sim_hardware_suggest' %}',
                            {'sim_code': data, 'include_sim_pk': {{ skno.sim_hardware.pk }}},
                            function(result) {
                                that.process(result);
                            }
                    );
                },
                property: 'sim_code',
                updater: function(item) {
                    return item['sim_code']
                }
            });

            $('#id_cashbox').typeahead({
                source: function (data) {
                    var that = this;
                    $.get('{% url 'core_cashbox_suggest' %}', {'cashbox': data}, function(result) {
                        that.process(result);
                    });
                },
                property: 'name',
                matcher: function() {return true},
                updater: function(item) {
                    return item['name']
                }
            });
        {% endif %}

        {% ifperms skno_passport_download %}
            $('a#skno_passport').click(function () {
                $.get("{% url 'device_skno_passport' skno.pk %}");
                show_message('Задание на формирование паспорта СКНО добавлено в очередь.');
                return false;
            });
        {% endifperms %}
    });
</script>
{% endblock %}