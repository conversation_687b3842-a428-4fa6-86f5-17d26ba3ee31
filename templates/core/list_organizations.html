{% extends 'layout.html' %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Организации</h3>
        </div>
        <a class="btn btn-primary pull-right" href="{% url 'core_organizations_create' %}">
             <span class="glyphicon glyphicon-plus"></span> Создать запись</a>

        {% if object_list %}
            <table class="table table-striped" style="margin-top: 60px">
                <thead>
                <tr>
                    <th>Дата регистрации</th>
                    <th>УНП</th>
                    <th>Наименование</th>
                    <th>СОАТО</th>
                    <th>Адрес</th>

                    <th style="width: 55px"></th>
                </tr>
                </thead>
                <tbody>
                {% for ol in object_list %}
                    <tr>
                        <td>{{ ol.register_date }}</td>
                        <td>{{ ol.unp }}</td>
                        <td>{{ ol.name }}</td>
                        <td>{{ ol.soato }}</td>
                        <td>{{ ol.address }}</td>

                        <td>
                            <a href="{% url 'core_organizations_edit' ol.pk %}" title="Редактировать" class="glyphicon glyphicon-edit"></a>
                            <a href="{% url 'core_trading_objects_list' ol.pk %}" title="Список торговых объектов" class="glyphicon glyphicon-shopping-cart"></a>

                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
        {% else %}
            <h4 style="text-align: center">Записей нет.</h4>
        {% endif %}

    </div>
{% endblock %}