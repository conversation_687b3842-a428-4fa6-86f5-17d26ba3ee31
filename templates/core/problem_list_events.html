{% extends 'layout.html' %}
{% load endless request_view humanize_raw_data jsonify %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>
                Проблемные события
            </h3>
        </div>

        <div class="row" style="margin: 20px 0">
            <div class="col-lg-10">
                {% include 'ww_filters/_list_forms.html' with type='problem_events_list' %}
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8 ">

                {% paginate object_list %}
                {% get_pages %}
                {% if object_list %}
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th>Время события</th>
                            <th>Код события</th>
                            <th>Номер КО</th>
                            <th>Номер КО в заголовке</th>
                            <th>Наименование</th>
                            <th>Дата получения</th>
                            <th>Код ошибки</th>
                            <th>Код оператора</th>
                            <th>Описание</th>
                            <th>Тип кассового оборудования</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for event in object_list %}
                            <tr  id='{{ event.pk }}' name="event" class='event'>
                                <td>{{ event.issued_at|time:'TIME_FORMAT' }}</td>
                                <td>{{ event.event_id }}</td>
                                <td>{{ event.cashbox_number|default_if_none:'' }}</td>
                                <td>{{ event.header_cashbox_number|default_if_none:'' }}</td>
                                <td>{{ event.get_event_display }}
                                <td>{{ event.received_at }}</td>
                                <td>{{ event.code|default_if_none:'' }}</td>
                                <td>{{ event.operator_code|default_if_none:'' }}</td>
                                <td>{{ event.description|default_if_none:'' }}</td>
                                <td>{{ event.cashbox_type|default_if_none:'' }}</td>
                                <td class="hiddenRowData" style="display: none">
                                    <dl>
                                        <dt>Пакет</dt>
                                        <pre style="min-height: 150px">{{ event.data|humanize_raw_data|default:' ' }}</pre>
                                        <dt>Значения</dt>
                                        <pre id="req_body_value"
                                             style="min-height: 150px">{{ event.vals|jsonify|default:' ' }}</pre>
                                    </dl>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                    {% show_pages %}
                    <div id="raw_data"></div>
                {% else %}
                    <h3 class="text-center">Записей нет.</h3>
                {% endif %}
            </div>
        </div>
    </div>
    <script type="text/javascript">
      $(document).ready(function () {
        function insert_text(el) {
          let data = el.text();
          if (!data || data === 'None') {
            return el.html('')
          }
          try {
            return el.html(JSON.stringify(JSON.parse(data), null, 4));
          } catch (e) {
            console.log('JSON parse Error');
          }
        }

        $('tr.event').click(function () {
          const positions = $('#position')
          const raw_data = $('#raw_data')
          positions.html('')
          raw_data.html('')
          $('tr').removeClass('success');
          $(this).addClass('success');
          $('#info').html($(this).find('.hiddenCheck').html());
          positions.html($(this).find('.hiddenPosition').html());
          insert_text($(this).find('#req_body_value'));
          raw_data.html($(this).find('.hiddenRowData').html());
        });
        });
    </script>

{% endblock %}