{% extends 'layout.html' %}

{% block content %}

    <div class="container">
        <div class="page-header">
            <h3>Файлы</h3>
        </div>
        <div class="row">
            <div class="col-sm-5">
                <ol class="breadcrumb">
                    {% for title, link in breadcrumbs %}
                        {% if forloop.last %}
                            <li class="active">{{ title }}</li>
                        {% else %}
                            <li><a href="?path={{ link }}">{{ title }}</a></li>
                        {% endif %}
                    {% endfor %}
                </ol>
            </div>
            <div class="col-sm-6">
                <div style="display: flex; justify-content: flex-end;">
                    <div class="dropdown" style="margin-right: 10px;">
                        <button id="select_directory_type" type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" >
                            Выбрать справочник<span class="caret"></span>
                        </button>
                        <ul class="dropdown-menu" role="menu">
                            {% for dir_id, dir_name in EXTERNAL_DIRECTORIES %}
                                <li><a data-id="{{ dir_id }}" class="select-directory-type" href="#">{{ dir_name }}</a></li>
                            {% endfor %}
                        </ul>
                    </div>
                    <label style="margin-right: 10px; margin-top: 7px; font-weight: normal;">
                        <input type="checkbox" id="full_update" /> Полное обновление
                    </label>
                    <button id="upload_dbf" type="button" class="btn btn-primary" style="margin-right: 5px;">
                        Загрузить DBF
                    </button>
                    <button id="upload_json" type="button" class="btn btn-primary" >
                        Загрузить JSON
                    </button>
                </div>
            </div>
        </div>
        <table class="table table-hover">
            <thead>
            <tr>
                <th style="width:30px"></th>
                <th>Имя файла</th>
                <th class="col-lg-2">Размер</th>
            </tr>
            </thead>
            <tbody>
                {% for d in dirs|dictsort:"name" %}
                    <tr>
                        <td>
                            <span class="glyphicon glyphicon-folder-close"></span>
                        </td>
                         <td colspan="3">
                             <a href="?path={{ d.path }}">{{ d.name }}</a>
                         </td>
                    </tr>
                {% endfor %}
                {% for file in files|dictsort:"name" %}
                    <tr>
                        <td>
                            <span class="glyphicon glyphicon-file"></span>
                        </td>
                        <td>
                            {{ file.name }}
                        </td>
                        <td>
                            {{ file.size|filesizeformat }}
                        </td>
                        <td>
                            {% if file %}
                                <input class="file-to-upload" type="radio" name="file_to_upload" value="{{ file.name }}" />
                            {% endif %}
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
        </div>
<script>
$(function() {
    // Переменная для хранения выбранного типа справочника
    var selectedDirectoryType = '';
    var selectedDirectoryName = 'Выбрать справочник';

    // Обработчик выбора типа справочника
    $('.select-directory-type').click(function() {
        selectedDirectoryType = $(this).data('id');
        selectedDirectoryName = $(this).text();
        $('#select_directory_type').html(selectedDirectoryName + '<span class="caret"></span>');
    });

    function show_message(type, message) {
      var mes_html_code =
        '<div style="position:fixed; z-index:1000000;top:52px; left:0;width: 100%;opacity:0.9">' +
            '<div class="container"><div class="row"><div class="col-lg-8 col-lg-offset-2">' +
                '<div id="custom-message" class="alert alert-' + type + '">' +
                    '<button type="button" class="close" data-dismiss="alert" aria-hidden="true">' +
                        '&times;' +
                    '</button>' +
                    message
                '</div>' +
            '</div>' +
        '</div>';
        $('#message_block').html(mes_html_code);
        setTimeout(function () {
            $('#custom-message').fadeOut();
        }, 4000);
    }
    
    // Функция загрузки файла
    function uploadFile(format) {
        // Проверка, что выбран тип справочника
        if (!selectedDirectoryType) {
            alert('Выберите тип справочника!');
            return;
        }

        // Проверка, что выбран файл
        var selectedFile = $('.file-to-upload:checked').val();
        if (!selectedFile) {
            alert('Выберите файл для загрузки!');
            return;
        }

        const dir_path = '{{ dir_path }}';
        // Проверка, выбрано ли полное обновление
        var fullUpdate = $('#full_update').is(':checked');
        var data = {
          'csrfmiddlewaretoken': '{{ csrf_token }}',
          'type': selectedDirectoryType,
          'format': format,
          'file': selectedFile,
          'file_path': dir_path,
          'full_update': (fullUpdate ? 1 : 0),
        };
        upload_json_btn.prop("disabled", true)
        upload_dbf_btn.prop("disabled", true)
        setTimeout(function () {
            upload_dbf_btn.prop("disabled", false)
            upload_json_btn.prop("disabled", false)
        }, 5000);
        $.post('{% url 'directory_upload_external_directory' %}', data)
            .done(function (result) {
                show_message('success', 'Успешный успех')
            })
            .fail(function (result) {
                show_message('danger', 'Ошибка')
            })
    }

    // Обработчики кнопок загрузки
    const upload_dbf_btn = $('#upload_dbf')
    upload_dbf_btn.click(function() {
        uploadFile('dbf');
    });

    const upload_json_btn = $('#upload_json')
    upload_json_btn.click(function() {
        uploadFile('json');
    });
})


</script>
{% endblock %}