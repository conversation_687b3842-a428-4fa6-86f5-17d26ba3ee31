{% extends 'layout.html' %}
{% load permissions table_sorting endless %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>
                Реестры поставленных (снятых) ПК
            </h3>
        </div>

        <div class="row" id="filter_div">
            <div class="col-lg-12">
                {% include 'ww_filters/_list_forms.html' with type='business_entity' %}
            </div>
        </div>

        {% paginate object_list %}

        {% if object_list %}
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Код оператора</th>
                        <th>Дата и время формирования реестра</th>
                        <th>Отчетный период</th>
                        <th>Статус сверки</th>
                        <th>Дата и время проведения сверки</th>
                        <th style="width: 65px"></th>
                    </tr>
                </thead>
                <tbody>
                    {% for doc in object_list %}
                        <tr>
                            <td>{{ doc.operator_code }}</td>
                            <td>{{ doc.registry_date|date:'DATETIME_WITH_SECONDS_FORMAT' }}</td>
                            <td>{{ doc.reporting_period|date }}</td>
                            <td>{{ doc.get_check_status_display }}</td>
                            <td>{{ doc.result_stop_date|date:'DATETIME_WITH_SECONDS_FORMAT' }}</td>
                            <td>
                                <div class="actions" style="white-space: nowrap">
                                    {% ifperms pcs_operator_registry_detail %}
                                        <a href="{% url 'core_list_pcs_registry_detail' doc.pk doc.registry_date|date:'Y-m-d' %}" title="Просмотр" class="glyphicon glyphicon-eye-open"></a>
                                    {% endifperms %}
                                    {% ifperms pcs_operator_registry_check_request_list %}
                                        <a href="{% url 'core_list_pcs_registry_requests' doc.pk doc.registry_date|date:'Y-m-d' %}" title="Список запросов" class="glyphicon glyphicon-list"></a>
                                    {% endifperms %}
                                    {% ifperms pcs_operator_registry_detail %}
                                        <a href="{% url 'core_list_pcs_registry_detail_load_request' doc.pk doc.registry_date|date:'Y-m-d' %}" title="Сохранить реестр в файл" class="glyphicon glyphicon glyphicon-log-in"></a>
                                        <a href="{% url 'core_list_pcs_registry_detail_load_response' doc.pk doc.registry_date|date:'Y-m-d' %}" title="Сохранить квитанцию в файл" class="glyphicon glyphicon glyphicon-log-out"></a>
                                    {% endifperms %}
                                    {% if doc.check_status == 1 %}
                                        {% ifperms pcs_operator_registry_check_start %}
                                            <a href="{% url 'core_list_pcs_registry_cashboxes_check' doc.operator_code doc.pk %}" title="Запуск сверки" class="glyphicon glyphicon-play"></a>
                                        {% endifperms %}
                                    {% else %}
                                        {% ifperms pcs_operator_registry_detail %}
                                            <a href="{% url 'core_list_pcs_registry_detail_load_result' doc.pk doc.registry_date|date:'Y-m-d' %}" title="Сохранить сверку в файл" class="glyphicon glyphicon-saved"></a>
                                        {% endifperms %}
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% show_pages %}
        {% else %}
            <h4 style="text-align: center">Записей нет.</h4>
        {% endif %}
    </div>

<script>
    $(document).ready(function () {
        $('span.remove-filter').css("display", "none");
    });
</script>
{% endblock %}