{% extends 'layout.html' %}
{% load request_view permissions secure_protocols %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Просмотр Запроса оператора ПКС</h3>
        </div>

        <div class="row" style="margin-top: 20px">
            <div>
                <dl class="dl-horizontal">
                    <dt>Код оператора</dt>
                    <dd>{{ request_log.operator_code }}</dd>
                    <dt>Код запроса</dt>
                    <dd>{{ request_log.request_type }}</dd>
                    <dt>Наименование запроса</dt>
                    <dd>{{ request_log.get_request_type_display }}</dd>
                    <dt>Дата и время запроса</dt>
                    <dd>{{ request_log.created_at|date:"d-m-Y G:i:s" }}</dd>
                    <dt>Заголовки запроса</dt>
                    <dd>{{ request_log.request_headers }}</dd>
                    <dt style="margin-right: 15px">Тело запроса</dt>
                    <pre id="req_body">{{ request_log.request_body }}</pre>
                    <dt style="margin-right: 15px">Квитанция</dt>
                    <pre id="req_receipt">{{ request_log.receipt }}</pre>
                </dl>
            </div>
        </div>

        <div class="row col-sm-6">
            <div class="col-md-8 pull-right" >
                <a href="{{ exit_url }}" class="btn btn-default">
                    <span>Выход</span>
                </a>
            </div>
        </div>
    </div>


    <script>

        $(document).ready(function () {
            insert_text('req_body');
            insert_text('req_receipt');

            function insert_text(id_el) {
                let el = document.getElementById(id_el)
                let data = el.innerHTML
                if (!data || data === 'None') {
                    return el.innerHTML = ''
                }
                try {
                    el.innerHTML = JSON.stringify(JSON.parse(data), null, 4);
                } catch (e) {
                    console.log('JSON parse Error');
                }
            }
        });

    </script>

{% endblock %}