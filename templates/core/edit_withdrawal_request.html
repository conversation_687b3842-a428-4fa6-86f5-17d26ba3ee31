{% extends 'layout.html' %}
{% load permissions bootstrap3 bootstrap_input_groups staticfiles %}

{% block js %}
    <script src="{% static "core/select2/select2.min.js" %}"></script>
    <script src="{% static "core/select2/select2_locale_ru.js" %}"></script>
{% endblock %}

{% block css %}
    <link href="{% static "core/select2/select2.css" %}" rel="stylesheet" media="screen">
    <link href="{% static "core/select2/select2-bootstrap.css" %}" rel="stylesheet" media="screen">
{% endblock %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Редактирование заявки</h3>
        </div>
        <form id="request_form" method="post" class="form-horizontal" enctype="multipart/form-data" style="margin-top: 40px">
            {% csrf_token %}
            <div class="row">
                <div class="col-sm-6">
                    <div class="form-group">
                        <label class="col-lg-4 control-label">Учетный номер КО</label>
                        <div class="col-lg-6">
                            <p class='form-control-static'>{{ form_initial.cashbox }}</p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-lg-4 control-label">Тип</label>
                        <div class="col-lg-6">
                            <p class='form-control-static'>{{ form_initial.type }}</p>
                        </div>
                    </div>

                    {% if 'status' in form.fields %}
                        {% bootstrap_field form.status layout='horizontal' field_class='col-lg-4' label_class='col-lg-4' %}
                    {% else %}
                        <div class="form-group">
                            <label class="col-lg-4 control-label">Статус</label>
                            <div class="col-lg-6">
                                <p class='form-control-static'>{{ form.instance.get_status_display }}</p>
                            </div>
                        </div>
                    {% endif %}

                    {% if 'priority' in form.fields %}
                        {% bootstrap_field form.priority layout='horizontal' field_class='col-lg-4' label_class='col-lg-4' %}
                    {% else %}
                        <div class="form-group">
                            <label class="col-lg-4 control-label">Приоритет</label>
                            <div class="col-lg-6">
                                <p class='form-control-static'>{{ form.instance.get_priority_display|default:'' }}</p>
                            </div>
                        </div>
                    {% endif %}

                    <div class="form-group">
                        <label class="col-lg-4 control-label">Время поступления заявки</label>
                        <div class="col-lg-6">
                            <p class='form-control-static'>
                                {{ form_initial.receipt_datetime|date:'DATETIME_WITH_SECONDS_FORMAT' }}
                            </p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-lg-4 control-label">Время начала работ по заявке</label>
                        <div class="col-lg-6">
                            <p class='form-control-static'>
                                {{ form_initial.start_datetime|date:'DATETIME_WITH_SECONDS_FORMAT' }}
                            </p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-lg-4 control-label">СРК</label>
                        <div class="col-lg-6">
                            <p class='form-control-static'>{{ form_initial.srk }}</p>
                        </div>
                    </div>

                    {% if 'engineer_registrar' in form.fields %}
                        {% bootstrap_field form.engineer_registrar layout='horizontal' field_class='col-lg-6' label_class='col-lg-4' %}
                        <script>
                            $('#id_engineer_registrar').select2();
                        </script>
                    {% else %}
                        <div class="form-group">
                            <label class="col-lg-4 control-label">Инженер-регистратор</label>
                            <div class="col-lg-6">
                                <p class='form-control-static'>{{ form_initial.engineer_registrar }}</p>
                            </div>
                        </div>
                    {% endif %}

                    <div class="form-group">
                        <label class="col-lg-4 control-label">Время назначения инженера-регистратора</label>
                        <div class="col-lg-6">
                            <p class='form-control-static'>
                                {{ form_initial.registrar_destination_datetime|date:'DATETIME_WITH_SECONDS_FORMAT' }}
                            </p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-lg-4 control-label">Технолог, назначивший инженера-регистратора</label>
                        <div class="col-lg-6">
                            <p class='form-control-static'>{{ form_initial.technologist }}</p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-lg-4 control-label">Инженер-электроник</label>
                        <div class="col-lg-6">
                            <p class='form-control-static'>{{ form_initial.engineer_electronics }}</p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-lg-4 control-label">Время назначения инженера-электроника</label>
                        <div class="col-lg-6">
                            <p class='form-control-static'>
                                {{ form_initial.electronic_destination_datetime|date:'DATETIME_WITH_SECONDS_FORMAT' }}
                            </p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-lg-4 control-label">Технолог, назначивший инженера-электроника</label>
                        <div class="col-lg-6">
                            <p class='form-control-static'>{{ form_initial.electronic_technologist }}</p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-lg-4 control-label">Срок выполнения наряда</label>
                        <div class="col-lg-6">
                            <p class='form-control-static'>{{ form_initial.equipment_cashbox_expire_date|date }}</p>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-lg-4 control-label">Время выполнения заявки</label>
                        <div class="col-lg-6">
                            {% if 'end_datetime' in form.fields and req.related_skno %}
                                <a href="{% url 'device_skno_edit' req.related_skno.pk %}" class="btn btn-default">
                                    Снять СКНО
                                </a>
                            {% else %}
                                <p class='form-control-static'>
                                    {{ form_initial.end_datetime|date:'DATETIME_WITH_SECONDS_FORMAT' }}
                                </p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-lg-4 control-label">Просрочена</label>
                        <div class="col-lg-6">
                            {% if form_initial.is_overdue %}<i class="glyphicon glyphicon-ok"></i>{% endif %}
                        </div>
                    </div>

                    {% if 'act_returned' in form.fields %}
                        {% bootstrap_field form.act_returned layout='horizontal' field_class='col-lg-6' label_class='col-lg-4' %}
                    {% elif req.status == 3 %}
                        <div class="form-group">
                            <label class="col-lg-4 control-label">Акт возвращен</label>
                            <div class="col-lg-6">
                                {% if form_initial.act_returned %}<i class="glyphicon glyphicon-ok"></i>{% endif %}
                            </div>
                        </div>
                    {% endif %}
                </div>

                <div class="col-sm-6">
                    {% if req.status >= 2 %}
                        {% ifperms request_performing_request_act_download %}
                            <a href="{% url 'core_performing_request_act' req.pk %}">Акт выполнения работ</a>
                        {% endifperms %}
                    {% endif %}
                </div>
            </div>
        </form>
        <div class="row col-lg-12">
            <div class="row col-lg-10 col-lg-offset-2">
                <button type="button" class="btn btn-primary"
                        onclick="$('#request_form').prop('action', '{{ request.path }}' + '?save=not_redirect').submit();">
                    Сохранить и продолжить редактирование
                </button>
                <input type="button" value="Сохранить" class="btn btn-default" onclick="$('#request_form').submit()"/>
                <a href="{{ req.get_edit_url }}" class="btn btn-default">
                    <span>Отменить</span>
                </a>
                <a href="{{ exit_url }}" class="btn btn-default">
                    <span>Выход</span>
                </a>

                {% if req.status == 1 %}
                    {% ifperms request_delete %}
                        <a href="{% url 'core_delete_request' req.pk %}" class="btn btn-danger"
                           onclick="return confirm('Вы действительно хотите удалить?')">
                            Удалить
                        </a>
                    {% endifperms %}

                    {% ifperms request_dispatch %}
                        <form action="{% url 'core_request_dispatch' req.pk %}" style="display: inline" method="post">
                            {% csrf_token %}
                            <input type="submit" class="btn btn-default pull-right" value="Отправить заявку в работу"/>
                        </form>
                    {% endifperms %}
                {% endif %}

            </div>
        </div>
    </div>
{% endblock %}