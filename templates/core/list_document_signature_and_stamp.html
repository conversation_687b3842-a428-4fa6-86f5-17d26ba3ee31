{% extends 'layout.html' %}
{% load permissions table_sorting endless %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Штампы и печати для заверения документов</h3>
        </div>

        {% ifperms document_signature_and_stamp_create %}
            <a class="btn btn-primary pull-right" href="{% url 'core_create_document_signature_and_stamp' %}">
                 <span class="glyphicon glyphicon-plus"></span> Создать запись</a>
        {% endifperms %}

        {% paginate object_list %}

        {% if object_list %}
            <table class="table table-striped" style="margin-top: 60px">
                <thead>
                    <tr>
                        <th {% sort_attributes params 'verbose_document' %}>Документ</th>
                        <th {% sort_attributes params 'responsible_person' %}>Ответсвенный</th>
                        <th {% sort_attributes params 'signature' %}>Подпись</th>
                        <th {% sort_attributes params 'stamp' %}>Штамп</th>
                        <th style="width: 47px"></th>
                    </tr>
                </thead>
                <tbody>
                    {% for obj in object_list %}
                        <tr>
                            <td>{{ obj.get_document_display }}</td>
                            <td>{{ obj.responsible_person }}</td>
                            <td>{% if obj.signature %}<i class="glyphicon glyphicon-ok"></i>{% endif %}</td>
                            <td>{% if obj.stamp %}<i class="glyphicon glyphicon-ok"></i>{% endif %}</td>
                            <td>
                                {% ifperms document_signature_and_stamp_edit %}
                                    <a href="{% url 'core_edit_document_signature_and_stamp' obj.pk %}" title="Редактировать" class="glyphicon glyphicon-edit"></a>
                                {% endifperms %}
                                {% ifperms document_signature_and_stamp_detail %}
                                    <a href="{{ obj.get_absolute_url }}" class="glyphicon glyphicon-eye-open"></a>
                                {% endifperms %}
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% show_pages %}
        {% else %}
            <h4 style="text-align: center">Записей нет.</h4>
        {% endif %}
    </div>
{% endblock %}