{% extends 'layout.html' %}
{% load permissions bootstrap3 bootstrap_input_groups secure_protocols %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>{{ organization.full_name }} <small>Редактирование доп. соглашения</small></h3>
        </div>

        {% include 'core/_business_entity_edit_tabs.html' with current='agreement_be_with_aiskko' pk=organization.pk %}

        <form method="post" class="form-horizontal" enctype="multipart/form-data" style="margin-top: 40px">
            {% csrf_token %}
            <div class="row">
                <div class="col-md-6">
                {% bootstrap_field agreement_form.number layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}
                {% bootstrap_date_field agreement_form.start_date layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}
                {% bootstrap_field agreement_form.note layout='horizontal' field_class='col-sm-6' label_class='col-lg-4' %}
                </div>

                <div class="col-md-6">
                    {% for scan in addit_agreement.scans.all %}
                        <dl class="dl-horizontal">
                            <dt>
                                <a href="
                                    {% if scan.name|slice:"-3:"|lower == 'pdf' %}
                                        {% url 'core_pdf_jsviewer' %}?file={% secure scan.calc_url %}
                                    {% else %}
                                        {% secure scan.calc_url %}
                                    {% endif %}
                                    "
                                    target="_blank" data-toggle="popover"
                                    data-content='<img src="{% secure scan.calc_thumbnail_url %}">'>
                                        {{ scan.name }}
                                </a>
                            </dt>
                            <dd>
                                <a onclick="return confirm('Вы уверены?');"
                               href="{% url 'core_delete_addit_agreement_be_with_aiskko_scan' scan.pk %}">
                                &times</a>
                            </dd>
                        </dl>
                    {% endfor %}

                    <div style="margin-top: 40px">
                        {% bootstrap_field agreement_scan_form.scan layout='horizontal' field_class='col-sm-6' label_class='col-lg-4' %}
                    </div>
                </div>
            </div>

            <div class="row col-sm-6">
                <div class="col-md-8 pull-right" >
                    <input type="submit" class="btn btn-primary" value="Сохранить"/>
                    <a href="{% url 'core_edit_addit_agreement_be_with_aiskko' addit_agreement.pk %}" class="btn btn-default">
                        <span>Отменить</span>
                    </a>
                    <a href="{{ exit_url }}" class="btn btn-default">
                        <span>Выход</span>
                    </a>
                    {% ifperms add_agreement_be_with_aiskko_delete %}
                        <a href="{% url 'core_delete_addit_agreement_be_with_aiskko' addit_agreement.pk %}"
                           onclick="return confirm('Вы действительно хотите удалить?')" class="btn btn-danger pull-right">Удалить</a>
                    {% endifperms %}
                </div>
            </div>
        </form>
    </div>

    <script>
        $(document).ready(function() {
            $('[data-toggle="popover"]').popover({trigger: 'hover', placement: 'left', html:true});
        });
    </script>
{% endblock %}