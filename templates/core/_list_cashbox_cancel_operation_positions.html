{% load bootstrap3 humanize %}
{% if error %}
    <h4>Сервер с данными временно не доступен</h4>
{% else %}
    <table class="table table-striped">
        {% if cancel_operation.operation_type == 14 %}
            <thead>
                <tr>
                    <th>Дата и время</th>
                    <th>Код СИ</th>
                    <th>Код УКЗ</th>
                    <th>Наименование</th>
                </tr>
            </thead>
            <tbody name="position-table">
                {% for position in object_list %}
                    <tr>
                        <td>{{ position.issued_at }}</td>
                        <td>
                            {% if position.marking_code %}
                                {% include "core/_code_btn.html" with name='marking_code' value=position.marking_code pk=position.pk %}
                            {% endif %}
                        </td>
                        <td>
                            {% if position.ukz_code %}
                                {% include "core/_code_btn.html" with name='ukz_code' value=position.ukz_code pk=position.pk %}
                            {% endif %}
                        </td>
                        <td>{{ position.product_name|default:'' }}</td>
                    </tr>
                {% endfor %}
            </tbody>
        {% endif %}
    </table>
{% endif %}
