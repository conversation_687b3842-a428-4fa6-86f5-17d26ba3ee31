{% extends 'layout.html' %}
{% load bootstrap_input_groups humanize endless permissions humanize_raw_data jsonify %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>
                Проблемные маркированные возвраты
            </h3>
        </div>

        <div class="row" style="margin: 20px 0">
            <div class="col-lg-10">
                {% include 'ww_filters/_list_forms.html' with type='problem_refund_operations_list' %}
            </div>
        </div>

        <div class="row">
            <div class="col-lg-8 ">

                {% paginate object_list %}
                {% get_pages %}
                {% if object_list %}
                    <table class="table table-striped">
                        <thead>
                        <tr>
                            <th>Код валюты</th>
                            <th>Код пользователя API маркировки</th>
                            <th>УНП продавца</th>
                            <th>Уникальный номер сообщения</th>
                            <th>Дата и время совершения операции</th>
                            <th>Дата получения</th>
                            <th>Код ошибки</th>
                            <th>Описание</th>
                            <th>Тип кассового оборудования</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for marking in object_list %}
                            <tr id='{{ marking.id }}' class='check'
                                name='check'>
                                <td>{{ marking.currency }}</td>
                                <td>{{ marking.user_marked_api|default:'' }}</td>
                                <td>{{ marking.unp }}</td>
                                <td>{{ marking.message_number }}</td>
                                <td>{{ marking.issued_at }}</td>
                                <td>{{ marking.received_at }}</td>
                                <td>{{ marking.code|default_if_none:'' }}</td>
                                <td>{{ marking.description|default_if_none:'' }}</td>
                                <td>{{ marking.cashbox_type|default_if_none:'' }}</td>
                                <td class="hiddenCheck" style="display: none">
                                    {% include "mark/marking_info.html" with marking=marking marking_type='refund' %}
                                </td>
                                <td class="hiddenRowData" style="display: none">
                                    <dl>
                                        <dt>Пакет</dt>
                                        <pre id="req_body_data"
                                             style="min-height: 150px">{{ marking.data|humanize_raw_data|default:' ' }}</pre>
                                        <dt>Значения</dt>
                                        <pre id="req_body_value"
                                             style="min-height: 150px">{{ marking.vals|jsonify|default:' ' }}</pre>
                                    </dl>
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                    {% show_pages %}
                    <div id="raw_data"></div>
                    <div id="position"></div>
                {% else %}
                    <h3 class="text-center">Записей нет.</h3>
                {% endif %}
            </div>

            <div class="col-lg-4" id="info"></div>
        </div>
    </div>

    <script type="text/javascript">
      $(document).ready(function () {
        function insert_text(el) {
          let data = el.text();
          if (!data || data === 'None') {
            return el.html('')
          }
          try {
            return el.html(JSON.stringify(JSON.parse(data), null, 4));
          } catch (e) {
            console.log('JSON parse Error');
          }
        }

        $('tr.check').click(function () {
          const positions = $('#position')
          const raw_data = $('#raw_data')
          positions.html('')
          raw_data.html('')
          $('tr').removeClass('success');
          $(this).addClass('success');
          $('#info').html($(this).find('.hiddenCheck').html());
          positions.html($(this).find('.hiddenPosition').html());
          insert_text($(this).find('#req_body_data'));
          insert_text($(this).find('#req_body_value'));
          raw_data.html($(this).find('.hiddenRowData').html());
        });
        $('[name=check]').click(function () {
          $.get(
            '{% url 'problem_marked_refund_operation_position_list' %}',
            {
              id: this.id,
            },
            function (response) {
              $('#position').html(response)
            }
          );
        });
      });
    </script>
{% endblock %}