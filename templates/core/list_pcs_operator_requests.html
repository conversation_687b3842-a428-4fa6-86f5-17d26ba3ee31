{% extends 'layout.html' %}
{% load bootstrap_input_groups humanize endless request_view %}

{% block js %}
<!-- highlight.js START -->
    <link rel="stylesheet"
          href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.13.1/styles/default.min.css">
    <script src="//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.13.1/highlight.min.js"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    <!-- highlight.js END -->
    <link rel="stylesheet"
          href="//cdnjs.cloudflare.com/ajax/libs/highlight.js/9.13.1/styles/tomorrow-night-eighties.min.css
    ">


{% endblock %}
{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>
                <h3>{{ organization.full_name }} <small>Запросы оператора ПКС (тестовая зона)</small></h3>
            </h3>
        </div>
        <div class="row">
            {% include 'core/_pcs_operator_detail_tabs.html' with current='requests' pk=organization.pk %}
        </div>
        <div class="row">
            <div class="col-lg-10">
                {% include 'ww_filters/_list_forms.html' with type='pcs_operators' %}
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">

                {% paginate requests %}
                {% get_pages %}
                {% if requests %}
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Код оператора</th>
                                <th>Код запроса</th>
                                <th>Наименование запроса</th>
                                <th>Код ответа</th>
                                <th>номер КО</th>
                                <th>Дата и время</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for request in requests %}
                                <tr id='{{ request.pk }}' class='check' style="cursor: pointer">

                                    <td>{{ request.operator_code }}</td>
                                    <td>{{ request.request_type }}</td>
                                    <td>{{ request.get_request_type_display }}</td>
                                    <td>{{ request.answer_code }}</td>
                                    <td>{{ request.cashbox_number }}</td>
                                    <td>{{ request.created_at|date:"d-m-Y G:i:s" }}</td>

                                    <td class="hiddenCheck" style="display: none">
                                        {% include "core/request_info.html" with request=request %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                    {% show_pages %}
                {% else %}
                    <h3 class="text-center">Записей нет.</h3>
                {% endif %}
            </div>

            <div class="col-md-6 info"></div>
        </div>
    </div>

    <script type="text/javascript">
        $(document).ready(function() {
            $('tr.check').click(function(){
                $('tr').removeClass('success');
                $(this).addClass('success');
                $('div.info').html($(this).find('.hiddenCheck').html());
                /*
                $('div.info').html("<b>Загружается... <i class='fa fa-spinner fa-spin'></i></b>")
                $.post('{% url 'core_check_info' %}?pk=' + $(this).attr('id') + '&cashbox_number=' + {{ cashbox.account_number }} + '&date={{ filter_date|date:"Y-m-d" }}',
                        function(data){
                            $('div.info').html(data)
                        }
                )*/
            });
        });
    </script>
{% endblock %}