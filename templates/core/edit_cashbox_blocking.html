{% extends 'layout.html' %}
{% load permissions %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Редактирование задания на блокирование (разблокирование) работы кассового оборудования</h3>
        </div>

        <dl class="dl-horizontal">
            <dt>Основание бл/разбл</dt><dd>{{ blocking_task.reason }}</dd>
            <dt>Пользователь</dt><dd>{{ blocking_task.user }}</dd>
            <dt>Команда</dt><dd>{{ blocking_task.get_command_display }}</dd>
            <dt>Дата создания</dt><dd>{{ blocking_task.created_at }}</dd>
            <dt>Дата начала бл/разбл КО</dt><dd>{{ blocking_task.start_date }}</dd>
            <dt>Дата завершения бл/разбл КО</dt><dd>{{ blocking_task.end_date|default:'' }}</dd>
            <dt>Дата включения</dt><dd>{{ blocking_task.edited_at|default:'' }}</dd>
            <dt>КО</dt><dd>{{ blocking_task.filter_cashboxes.all|join:', '|default:'' }}</dd>
            <dt>Модель КО</dt><dd>{{ blocking_task.filter_cashbox_models.all|join:', '|default:'' }}</dd>
            <dt>СПД</dt><dd>{{ blocking_task.filter_business_entity.all|join:', '|default:'' }}</dd>
            <dt>Торговый объект</dt><dd>{{ blocking_task.filter_trading_object.all|join:', '|default:'' }}</dd>
            <dt>Описание</dt><dd>{{ blocking_task.description|default:''  }}</dd>
            <dt>Включено</dt><dd>{% if blocking_task.enabled == False %} Нет {% else %}Да{% endif %}</dd>
            <dt>Список номеров КО подлежащих бл/разбл</dt><dd>{{ blocking_task.result_cashboxes.all|join:', '|default:'' }}</dd>
        </dl>


        <div class="row col-md-6 col-sm-9" style="margin-top: 15px">
            <div class="col-md-2 col-sm-2"></div>
            <div class="col-md-2 col-sm-2">
                <a href="{{ exit_url }}" class="btn btn-default">
                    <span>Выход</span>
                </a>
            </div>

            <div class="col-md-2 col-sm-3">
                {% if blocking_task.enabled == False %}
                    <form method="post">
                        {% csrf_token %}
                        <input type="submit" class="btn btn-primary" value="Включить"/>
                    </form>
                {% endif %}
            </div>

            <div class="col-md-2 col-sm-3 pull-right">
                {% if blocking_task.enabled == False %}
                    {% ifperms cashbox_blocking_task_delete %}
                        <a href="{% url 'cashbox_blocking_task_delete' blocking_task.pk %}"
                           onclick="return confirm('Вы действительно хотите удалить?')"
                           class="btn btn-danger">
                            Удалить
                        </a>
                    {% endifperms %}
                {% endif %}
            </div>
        </div>

    </div>

{% endblock %}


