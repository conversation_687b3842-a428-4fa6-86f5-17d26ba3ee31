{% extends 'layout.html' %}
{% load permissions secure_protocols %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>{{ object.business_entity.full_name }} <small>Просмотр информации о договоре с ЦТО</small></h3>
        </div>

        {% include 'core/_business_entity_edit_tabs.html' with current='agreement' pk=object.business_entity.pk %}

        <div class="row">
            <div class="col-md-6">
                <dl class="dl-horizontal">
                    <dt>СПД</dt><dd>{{ object.business_entity }}</dd>
                    <dt>ЦТО</dt><dd>{{ object.center }}</dd>
                    <dt>Номер договора</dt><dd>{{ object.number }}</dd>
                    <dt>Срок действия договора с</dt><dd>{{ object.valid_from|date }}</dd>
                    <dt>по</dt><dd>{{ object.valid_to|date }}</dd>
                    <dt>Продление срока договора</dt><dd>{{ object.get_extension_display }}</dd>
                    <dt>Дата расторжения договора</dt><dd>{{ object.cancel_date|date }}</dd>
                    <dt>Данные были изменены/добавлены СПД</dt>
                    <dd>{% if object.last_update_by_be %}<i class="glyphicon glyphicon-ok"></i>{% endif %}</dd>
                    <dt>Примечание</dt><dd>{{ object.note }}</dd>
                </dl>
            </div>

            <div class="col-md-6">
                <dl class="dl-horizontal">
                    {% for scan in object.scans.all %}
                        <dt></dt>
                        <dd>
                            <a href="
                                {% if scan.name|slice:"-3:"|lower == 'pdf' %}
                                    {% url 'core_pdf_jsviewer' %}?file={% secure scan.calc_url %}
                                {% else %}
                                    {% secure scan.calc_url %}
                                {% endif %}
                                "
                                target="_blank" data-toggle="popover"
                                data-content='<img src="{% secure scan.calc_thumbnail_url %}">'>
                                    {{ scan.name }}
                            </a>
                        </dd>
                    {% endfor %}
                </dl>
            </div>
        </div>

        <div class="row col-sm-6">
            <div class="col-md-8 pull-right" >
                {% ifperms agreement_edit %}
                    <a href="{% url 'core_edit_agreement_with_pcs' object.pk %}" class="btn btn-default">
                        <span>Редактировать</span>
                    </a>
                {% endifperms %}
                <a href="{{ exit_url }}" class="btn btn-default">
                    <span>Выход</span>
                </a>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $('[data-toggle="popover"]').popover({trigger: 'hover', placement: 'left', html:true});
        });
    </script>
{% endblock %}