{% extends 'layout.html' %}
{% load secure_protocols table_sorting endless permissions %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>
                {{ organization.full_name }} <small>Выставленные акты</small>
            </h3>
        </div>
        {% if organization.non_resident %}
            {% include 'core/_business_entity_non_resident_edit_tabs.html' with current='view_acts' pk=pk %}
        {% else %}
            {% include 'core/_business_entity_edit_tabs.html' with current='view_acts' pk=pk %}
        {% endif %}

        <div class="row" style="margin: 30px 0">
            <div class="col-lg-12">
                <div class="row">
                    {% include 'ww_filters/_list_forms.html' with type='statistical_report' %}
                </div>
            </div>
        </div>


        {% paginate act_list %}

        {% if act_list %}
            <div id="act_form">
                <table class="table table-striped" style="margin-top: 20px">
                    <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="updator_all_checkbox">
                        </th>
                        <th {% sort_attributes params 'name' %}>
                            Наименование
                        </th>
                        <th {% sort_attributes params 'created_at' %}>
                            Время формирования акта
                        </th>
                        <th {% sort_attributes params 'was_viewed' %}>
                            Просмотрено
                        </th>
                        <th>
                            Акт
                        </th>
                        <th></th>
                    </tr>
                    </thead>
                    <tbody id="table_body">
                    {% for act in act_list %}
                        <tr {% if not act.was_viewed %}class="success"{% endif %}>
                            <td>
                                <input type="checkbox" name="act_pk"
                                       value="{{ act.pk }}" id="{{ act.pk }}">
                            </td>
                            <td style="max-width: 450px">{{ act.name }}</td>
                            <td>{{ act.created_at|date:'DATETIME_WITH_SECONDS_FORMAT' }}</td>
                            <td name="was_viewed_column">{% if act.was_viewed %}<i class="glyphicon glyphicon-ok"></i>{% endif %}</td>

                            <td>
                                {% if act.file %}
                                    <a name="act_file_link"
                                       data-act_pk="{{ act.pk }}"
                                       href="{% secure act.get_file_url %}"
                                       class="glyphicon glyphicon-list-alt"
                                       title="Скачать акт"></a>
                                {% endif %}
                            </td>

                            <td>
                                <div class="row">
                                    {% if act.act_type == service_maintenance_act_type %}
                                        <form method="post"
                                              enctype="multipart/form-data">
                                            {% csrf_token %}
                                            {% ifperms reload_be_act or add_correct_be_act %}
                                                <div class="col-lg-6">
                                                    <input type="file"
                                                           name="uploaded_file">
                                                </div>
                                                {% ifperms reload_be_act %}
                                                    <div class="{% ifperms add_correct_be_act %}col-lg-3{% else %}col-lg-6{% endifperms %}">
                                                        <button type="submit"
                                                                formaction="{% url 'core_reload_act' act.pk %}"
                                                                id="reload_act"
                                                                value="reload_{{ act.pk }}"
                                                                class="btn btn-primary btn-block">
                                                            Заменить
                                                        </button>
                                                    </div>
                                                {% endifperms %}
                                                {% ifperms add_correct_be_act %}
                                                    <div class="{% ifperms reload_be_act %}col-lg-3{% else %}col-lg-6{% endifperms %}">
                                                        <button type="submit"
                                                                formaction="{% url 'add_correct_act' act.pk %}"
                                                                id="add_act"
                                                                value="add_{{ act.pk }}"
                                                                class="btn btn-primary btn-block">
                                                            Добавить
                                                        </button>
                                                    </div>
                                                {% endifperms %}
                                            {% endifperms %}
                                        </form>
                                    {% elif act.act_type == correct_service_maintenance_act_type %}
                                        <form method="post"
                                              action="{% url 'remove_correct_act' act.pk %}"
                                              enctype="multipart/form-data">
                                            {% csrf_token %}
                                            {% ifperms remove_correct_be_act %}
                                                <div class="col-sm-offset-6 col-lg-6">
                                                    <button type="submit"
                                                            id="remove_act"
                                                            value="{{ act.pk }}"
                                                            class="btn btn-primary btn-block">
                                                        Удалить акт
                                                    </button>
                                                </div>
                                            {% endifperms %}
                                        </form>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                {% show_pages %}
                {% ifperms act_delete %}
                    <div>
                        <button type="button" id="delete_selected_act"
                                class="btn btn-default">
                            Удалить отмеченные акты
                        </button>
                    </div>
                {% endifperms %}
            </div>
        {% else %}
            <h4 style="text-align: center">Актов нет.</h4>
        {% endif %}
    </div>

    <script>
      $(document).ready(function () {
        function update_all_organization_checkbox(val) {
          $('[name="act_pk"]').each(function () {
            $(this).prop('checked', val);
          })
        }

        $('#updator_all_checkbox').click(function () {
          update_all_organization_checkbox($(this).is(':checked'));
        });

        $('#delete_selected_act').click(function () {
          var form_data_str = '';
          $("[name='act_pk']:checked").each(function () {
            var item = $(this);
            form_data_str = form_data_str + '&' + item.attr('name') + '=' + item.val();
          });


          function show_message(message, message_type, time_fade) {
            if (message) {
              message_type = message_type || 'success';
              time_fade = time_fade || 4000;

              var mes_html_code = '<div style="position:fixed; z-index:1000000;top:52px; left:0;width: 100%;opacity:0.9">' +
                '<div class="container"><div class="row"><div class="col-lg-8 col-lg-offset-2">' +
                '<div id="custom-message" class="alert alert-' + message_type + '">' +
                '<button type="button" class="close" data-dismiss="alert" aria-hidden="true">' +
                '&times;' +
                '</button>' +
                message +
                '</div>' +
                '</div>' +
                '</div>';

              $('#message_block').html(mes_html_code);
              setTimeout(function () {
                $('#custom-message').fadeOut();
              }, time_fade);
            }
          }

          if (form_data_str === "") {
            show_message('Ни один акт не выделен.', 'danger');
          } else {
            window.location.href = "{% url 'core_delete_act' %}" + '?org=' + {{ organization.pk }} + form_data_str;
            show_message('Акты удалены.');
          }

        });

        $('#table_body').on('click', '[name=act_file_link]', {}, function () {
          var row = $(this).parents('tr');
          var cell = row.find('td[name=was_viewed_column]');

          if (cell.html() == '') {
            // if act don't viewed
            $.post("{% url 'core_mark_viewed_act' %}", $(this).data(), function () {
            });
            row.removeClass();
            cell.append('<i class="glyphicon glyphicon-ok"></i>');

            var badge_span = $('span.badge');
            var count_not_viewed = parseInt(badge_span.text());
            if (count_not_viewed == 1) {
              badge_span.remove();
            } else {
              badge_span.text(count_not_viewed - 1);
            }
          }
        })
      })
    </script>
{% endblock %}