{% extends 'layout.html' %}
{% load staticfiles bootstrap3 bootstrap_input_groups %}

{% block js %}
    <script src="{% static "core/js/bootstrap3-typeahead.js" %}"></script>
    <script src="{% static "core/select2/select2.min.js" %}"></script>
    <script src="{% static "core/select2/select2_locale_ru.js" %}"></script>
    <script type="text/javascript" src="{% static 'core/js/jquery.formset.js'%}"></script>
{% endblock %}

{% block css %}
    <link href="{% static "core/select2/select2.css" %}" rel="stylesheet" media="screen">
    <link href="{% static "core/select2/select2-bootstrap.css" %}" rel="stylesheet" media="screen">
{% endblock %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Создание задания на блокирование (разблокирование) работы кассового оборудования</h3>
        </div>

        <div class="container">
            {% include 'core/cashblock_blocking_create_tabs.html' with active="trading_object" %}
        </div>

        <form method="post" class="form-horizontal">
            {% csrf_token %}
            {% bootstrap_field form.reason layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}
            {% bootstrap_field form.command layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}

             <div class="row">
                <div class="col-sm-4">
                    {% bootstrap_date_field form.start_date layout='horizontal' field_class='col-sm-6' label_class='col-lg-6'%}
                </div>
                <div class="col-sm-4">
                    {% bootstrap_date_field form.end_date layout='horizontal' field_class='col-sm-6' label_class='col-lg-4' %}
                </div>
            </div>

            {% bootstrap_field form.unp layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}
            {% bootstrap_field form.filter_trading_object layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}
            {% bootstrap_field form.description layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}

            <div class="row col-sm-6" style="margin-top: 20px">
                <div class="col-md-8 pull-right" >
                    <input type="submit" class="btn btn-primary" value="Сохранить"/>
                    <a href="{% url 'core_create_cashbox-blocking' %}" class="btn btn-default">
                        <span>Отменить</span>
                    </a>
                    <a href="{{ exit_url }}" class="btn btn-default">
                        <span>Выход</span>
                    </a>
                </div>
            </div>
        </form>
    </div>
    <script>
        $(document).ready(function () {
            $('#id_unp').typeahead({
                source: function (data) {
                    var that = this;

                    $.get('{% url 'report_be_suggest' %}', {'search_request': data}, function (result) {
                        that.process(result);
                    });
                },
                property: 'name',
                matcher: function () {
                    return true
                },
                updater: function (item) {
                    let unp = item['unp']
                    get_trading_object(unp)
                    return unp

                }
            });

            $(function () {
                $("#id_filter_trading_object").select2()
            })

            function get_trading_object(unp) {
                $.get('{% url 'get_trading_object' %}', {'search_request': unp}, function (result) {
                    for (let i = 0; i < result.length; i++) {
                        let newOption = new Option(result[i].name, result[i].id, false, false);
                        $("#id_filter_trading_object").append(newOption).trigger('change');
                    }
                });
            }

        });
    </script>
{% endblock %}