{% extends 'layout.html' %}
{% load bootstrap3 bootstrap_input_groups staticfiles permissions %}

{% block js %}
    <script src="{% static "core/select2/select2.min.js" %}"></script>
    <script src="{% static "core/select2/select2_locale_ru.js" %}"></script>
{% endblock %}

{% block css %}
    <link href="{% static "core/select2/select2.css" %}" rel="stylesheet" media="screen">
    <link href="{% static "core/select2/select2-bootstrap.css" %}" rel="stylesheet" media="screen">
{% endblock %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>{{ organization.full_name }} <small>Создание заявки</small></h3>
        </div>

        <form method="post" class="form-horizontal" enctype="multipart/form-data" style="margin-top: 40px">
            {% csrf_token %}
            <div class="row">
                {% bootstrap_field form.cashbox layout='horizontal' field_class='col-sm-3' label_class='col-lg-2' %}
                {% bootstrap_field form.type layout='horizontal' field_class='col-sm-3' label_class='col-lg-2' %}
                {% bootstrap_datetime_field form.receipt_datetime layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}
                {% if form.non_field_errors %}
                    <div class="text-danger" style="margin-bottom: 20px">
                        {% for error in form.non_field_errors %}
                            {{ error }}
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
            <div class="row col-sm-12">
                <div class="col-lg-12 col-lg-offset-2">
                    {% ifperms request_dispatch or request_status_change or request_priority_change or request_engineer_registrar_purpose or request_skno_equipment %}
                        <button type="submit" class="btn btn-primary" name="save" value="redirect_to_edit">
                            Сохранить и перейти к редактированию
                        </button>
                    {% endifperms %}
                    <input type="submit" value="Сохранить" name="save"
                           class="btn btn-{% ifperms request_dispatch or request_status_change or request_priority_change or request_engineer_registrar_purpose or request_skno_equipment %}default{% else %}primary{% endifperms %}"/>
                    <a href="{% url 'core_create_request_by_be' organization.pk %}" class="btn btn-default">
                        <span>Отменить</span>
                    </a>
                    <a href="{{ exit_url }}" class="btn btn-default">
                        <span>Выход</span>
                    </a>
                </div>
            </div>
        </form>
    </div>
<script>
    $(document).ready(function(){
        $("#id_cashbox").select2();
    });
</script>
{% endblock %}