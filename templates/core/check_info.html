{% load bootstrap3 humanize math %}
{% block content %}
{#    <img width="75px" height="75px" src='{% url 'generate_qr_link' %}?check={{ check_info.pk }}&cashbox={{ cashbox_number }}'>#}
    {% if error %}
        <h4>Сервер с данными временно не доступен</h4>
    {% else %}
        {% if not problem %}
            <div id="{{ check.pk }}">
                {% if  not flag %}
                    <h4 {% if not check.is_wrong %}style="display: none"{% endif %}>
                        <strong>Чек отмечен как некорректный!</strong>
                    </h4>

                    {% if 'cash_box_mark_wrong_documents' in request.account.all_perms %}
                        <button
                            data-unique-id="{{ check.unique_id }}"
                            data-check-pk="{{ check.pk }}"
                            data-document-date="{{ check.issued_at|date }}"
                            class="btn {% if check.is_wrong %}btn-unmark{% else %}btn-mark{% endif %} btn-block move-button"
                        >

                            {% if check.is_wrong %}Отметить как корректный{% else %}Отметить как некорректный{% endif %}
                            {% endif %}
                        </button>
                {% endif %}
            </div>
        {% endif %}

        <table class="table-no-border" style="margin-top: 20px; max-width: 350px">
            <thead>
                <tr>
                    <th></th>
                    <th></th>
                    {% if check.currency != 'BYN' and 'can_view_data_currency' in request.account.all_perms %}
                        <th>BYN</th>
                    {% endif %}
                </tr>
            </thead>
            <tbody>
            <tr>
                <th>Уникальный номер</th>
                <td>{{ check.unique_id|upper }}</td>
                <td></td>
            </tr>
            <tr>
                <th>Номер чека</th>
                <td>{{ check.number }}</td>
                <td></td>
            </tr>
            <tr>
                <th>Кассир</th>
                <td>{{ check.cashier }}</td>
                <td></td>
            </tr>
            <tr>
                <th>Дата и время выдачи платежного документа</th>
                <td>{{ check.issued_at }}</td>
                <td></td>
            </tr>
            <tr>
                <th>Наименование (код) валюты</th>
                <td>{{ check.currency }}</td>
                <td></td>
            </tr>
            <tr>
                <th>Количество позиций товара в платежном документе</th>
                <td>{{ check.positions_count }}</td>
                <td></td>
            </tr>
            <tr>
                <th>УНП третьего лица</th>
                <td>{{ check.unp_third|default:'' }}</td>
                <td></td>
            </tr>
            <tr>
                <th>Итого общая стоимость услуг/товаров</th>
                {% if check.is_wrong %}
                    <td>{{ check.extra.wrong_amount.total_amount|intcomma }}</td>
                {% else %}
                    <td>{{ check.total_amount|intcomma }}</td>
                {% endif %}

                {% if check.currency != 'DBDBDBFF' and 'can_view_data_currency' in request.account.all_perms %}
                    {% if check.is_wrong %}
                        <td>{{ check.extra.wrong_amount.total_amount_in_byn|intcomma }}</td>
                    {% else %}
                        <td>{{ check.total_amount_in_byn|default_if_none:''|intcomma }}</td>
                    {% endif %}
                {% endif %}
            </tr>

            <tr>
                <th>Сумма скидки</th>
                {% if check.is_wrong%}
                    <td>{{ check.extra.wrong_amount.discount_amount|default_if_none:'0,00'|intcomma }}</td>
                {% else %}
                    <td>{{ check.discount_amount|default_if_none:'0,00'|intcomma }}</td>
                {% endif %}

                {% if check.currency != 'BYN' and 'can_view_data_currency' in request.account.all_perms %}
                    {% if check.is_wrong%}
                        <td>{{ check.extra.wrong_amount.discount_amount_in_byn|default_if_none:'0,00'|intcomma }}</td>
                    {% else %}
                        <td>{{ check.discount_amount_in_byn|default_if_none:'0,00'|intcomma }}</td>
                    {% endif %}
                {% endif %}
            </tr>

            <tr>
                <th>Сумма наценки</th>
                {% if check.is_wrong %}
                    <td>{{ check.extra.wrong_amount.surcharge_amount|default_if_none:'0,00'|intcomma }}</td>
                {% else %}
                    <td>{{ check.surcharge_amount|default_if_none:'0,00'|intcomma }}</td>
                {% endif %}

                {% if check.currency != 'BYN' and 'can_view_data_currency' in request.account.all_perms %}
                    {% if check.is_wrong %}
                        <td>{{ check.extra.wrong_amount.surcharge_amount_in_byn|default_if_none:''|intcomma }}</td>
                    {% else %}
                        <td>{{ check.surcharge_amount_in_byn|default_if_none:''|intcomma }}</td>
                    {% endif %}
                {% endif %}
            </tr>

            <tr>
                <th>Сумма скидки/наценки</th>
                {% if check.is_wrong %}
                    <td>{{ check.extra.wrong_amount.margin_amount|default_if_none:'0,00'|intcomma }}</td>
                {% else %}
                    <td>{{ check.margin_amount|default_if_none:'0,00'|intcomma }}</td>
                {% endif %}

                {% if check.currency != 'BYN' and 'can_view_data_currency' in request.account.all_perms %}
                    {% if check.is_wrong %}
                        <td>{{ check.extra.wrong_amount.margin_amount_in_byn|default_if_none:'0,00'|intcomma }}</td>
                    {% else %}
                        <td>{{ check.margin_amount_in_byn|default_if_none:'0,00'|intcomma }}</td>
                    {% endif %}
                {% endif %}
            </tr>

            <tr>
                <th>Итого к оплате</th>
                {% if check.is_wrong %}
                    <td>{{ check.extra.wrong_amount.payment_amount|intcomma }}</td>
                {% else %}
                    <td>{{ check.payment_amount|intcomma }}</td>
                {% endif %}

                {% if check.currency != 'BYN' and 'can_view_data_currency' in request.account.all_perms %}
                    {% if check.is_wrong %}
                        <td>{{ check.extra.wrong_amount.payment_amount_in_byn|intcomma }}</td>
                    {% else %}
                        <td>{{ check.payment_amount_in_byn|default_if_none:''|intcomma }}</td>
                    {% endif %}
                {% endif %}
            </tr>

            <tr>
                <th>Итого безналичными</th>
                {% if check.is_wrong %}
                    <td>{{ check.extra.wrong_amount.clearing_amount|intcomma }}</td>
                {% else %}
                    <td>{{ check.clearing_amount|intcomma }}</td>
                {% endif %}

                {% if check.currency != 'BYN' and 'can_view_data_currency' in request.account.all_perms %}
                    {% if check.is_wrong %}
                        <td>{{ check.extra.wrong_amount.clearing_amount_in_byn|intcomma }}</td>
                    {% else %}
                        <td>{{ check.clearing_amount_in_byn|default_if_none:''|intcomma }}</td>
                    {% endif %}
                {% endif %}
            </tr>

            <tr>
                <th>Итого наличными</th>
                {% if check.is_wrong %}
                    <td>{{ check.extra.wrong_amount.cash_amount|intcomma }}</td>
                {% else %}
                    <td>{{ check.cash_amount|intcomma }}</td>
                {% endif %}

                {% if check.currency != 'BYN' and 'can_view_data_currency' in request.account.all_perms %}
                    {% if check.is_wrong %}
                        <td>{{ check.extra.wrong_amount.cash_amount_in_byn|intcomma }}</td>
                    {% else %}
                        <td>{{ check.cash_amount_in_byn|default_if_none:''|intcomma }}</td>
                    {% endif %}
                 {% endif %}
            </tr>

            <tr>
                <th>Итого другими способами оплаты</th>
                {% if check.is_wrong %}
                    <td>{{ check.extra.wrong_amount.another_amount|intcomma }}</td>
                {% else %}
                    <td>{{ check.another_amount|intcomma }}</td>
                {% endif %}

                {% if check.currency != 'BYN' and 'can_view_data_currency' in request.account.all_perms %}
                    {% if check.is_wrong %}
                        <td>{{ check.extra.wrong_amount.another_amount_in_byn|intcomma }}</td>
                    {% else %}
                        <td>{{ check.another_amount_in_byn|default_if_none:''|intcomma }}</td>
                    {% endif %}
                {% endif %}
            </tr>

            <tr>
                <td></td>
            </tr>
            </tbody>
        </table>

    {% endif %}

     <script>
        $(document).ready(function() {
            $('[name=trad_item]').on('click', '[name=modal_window_link]', function() {
                $.get('{% url 'core_trad_item_full_info' %}', {'gtin': $(this).text()}, show_modal_window);
                return false;
            });
            $('.table-no-border tbody tr:nth-child(odd)').addClass('byn');

        });
    </script>

    <style>
        .byn {
            background-color: #f9f9f9;
        }
    </style>

{% endblock %}