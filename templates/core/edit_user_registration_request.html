{% extends 'layout.html' %}
{% load staticfiles bootstrap3 bootstrap_input_groups permissions %}

{% block js %}
    <script type="text/javascript" src="{% static 'core/moment/moment.js'%}"></script>
    <script type="text/javascript" src="{% static 'core/bootstrap-datetimepicker/build/js/bootstrap-datetimepicker.min.js'%}"></script>
    <script type="text/javascript" src="{% static 'core/bootstrap-datetimepicker/ru.js'%}"></script>
    <script type="text/javascript" src="{% static 'core/js/jquery.formset.js'%}"></script>
{% endblock %}

{% block css %}
    <link rel="stylesheet" href="{% static 'core/bootstrap-datetimepicker/build/css/bootstrap-datetimepicker.min.css'%}" />
{% endblock %}

{% block content %}
    <div class="container">
        <div class="page-header"><h3>Редактирование заявки на регистрацию пользователей</h3></div>

        <form method="post" class="form-horizontal" enctype="multipart/form-data" style="margin-top: 40px">
            {% csrf_token %}
            <div id="formset" class="row">
                <div class="col-sm-6">
                    {% bootstrap_datetime_field form.create_datetime layout='horizontal' field_class='col-sm-6' label_class='col-lg-4' %}
                </div>
                <div class="col-sm-12">
                    {{ formset.management_form }}
                    <div class="row">
                        <div class="row">
                            <div class="col-sm-11" style="margin-bottom: 20px">
                                <div class="col-lg-3 text-center">Личный номер <span class="required">*</span></div>
                                <div class="col-lg-3 text-center">Роль <span class="required">*</span></div>
                                <div class="col-lg-5 text-center">ФИО</div>
                                <div class="col-lg-1 text-center">ИМНС</div>
                            </div>
                        </div>

                        {% if formset_is_empty %}
                            <div class="row col-lg-12 text-danger" style="margin-bottom: 20px">
                                Заявка должна содержать хотя бы один личный номер пользователя на регистрацию
                            </div>
                        {% endif %}

                        {% for f in formset %}
                            <div class="formset col-sm-12 form-group">
                                <div class="col-sm-11">
                                    <div class="col-sm-3">
                                        {% bootstrap_field f.personal_number layout='horizontal' field_class='col-sm-12' show_label=False %}
                                    </div>
                                    <div class="col-sm-3">
                                        {% bootstrap_field f.role layout='horizontal' field_class='col-sm-12' show_label=False %}
                                    </div>
                                    <div class="col-sm-5">
                                        {% bootstrap_field f.user_full_name layout='horizontal' field_class='col-sm-12' show_label=False %}
                                    </div>
                                    <div class="col-sm-1">
                                        {% bootstrap_field f.imns layout='horizontal' field_class='col-sm-12' show_label=False %}
                                        {{ f.id }}
                                        {% if formset.can_delete %}{{ f.DELETE }}{% endif %}
                                    </div>
                                </div>
                                <div class="col-sm-1">
                                    <a class="delete close">&times;</a>
                                </div>
                            </div>
                        {% endfor %}

                        <div class="form-group col-sm-12">
                            <a id="add_form" type="button" style="cursor: pointer">Добавить пользователя</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row col-sm-6">
                <div class="col-md-8 pull-right" >
                    <input type="submit" class="btn btn-primary" value="Сохранить"/>
                    <a href="{% url 'core_edit_user_registration_request' user_reg_request.pk %}" class="btn btn-default">
                        <span>Отменить</span>
                    </a>
                    <a href="{{ exit_url }}" class="btn btn-default">
                        <span>Выход</span>
                    </a>
                    {% ifperms user_registration_request_delete %}
                        <a href="{% url 'core_delete_user_registration_request' user_reg_request.pk %}"
                           onclick="return confirm('Вы действительно хотите удалить?')" class="btn btn-danger pull-right">Удалить
                        </a>
                    {% endifperms %}
                </div>
            </div>
        </form>
    </div>
    <script>
        $(document).ready(function() {
            $('#datetimepicker1').datetimepicker({
                language: 'ru'
            });

            $('#formset').formset({form_can_delete: true});
        });
    </script>
{% endblock %}