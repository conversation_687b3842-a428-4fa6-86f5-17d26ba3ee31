{% extends 'layout.html' %}
{% load bootstrap3 bootstrap_input_groups staticfiles permissions %}

{% block js %}
    <script src="{% static "core/js/bootstrap3-typeahead.js" %}"></script>
    <script src="{% static "core/select2/select2.min.js" %}"></script>
    <script src="{% static "core/select2/select2_locale_ru.js" %}"></script>
    <script type="text/javascript" src="{% static 'core/js/jquery.formset.js'%}"></script>
{% endblock %}

{% block css %}
    <link href="{% static "core/select2/select2.css" %}" rel="stylesheet" media="screen">
    <link href="{% static "core/select2/select2-bootstrap.css" %}" rel="stylesheet" media="screen">
{% endblock %}

{% block content %}
    <div class="container">

        <div class="page-header">
            <h3>Сообщение оператору ПКС <small>Создание </small></h3>
        </div>


        <form method="post" class="form-horizontal" style="margin-top: 40px">
            {% csrf_token %}
            <div class="row">
                <div class="col-sm-12">
                    {% bootstrap_field form.operator_code layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}
                    {% bootstrap_field form.message_type layout='horizontal' field_class='col-sm-3' label_class='col-lg-4' %}
                    {% bootstrap_field form.cashbox_number layout='horizontal' field_class='col-sm-2' label_class='col-lg-4' %}
                    {% bootstrap_field form.unp layout='horizontal' field_class='col-sm-2' label_class='col-lg-4' %}
                    {% bootstrap_field form.message_text layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}

            {% if form.non_field_errors %}
                <div class="row text-center" style="margin-bottom: 30px">
                    {% for error in form.non_field_errors %}
                        <span class="text-danger">{{ error }}</span>
                    {% endfor %}
                </div>
            {% endif %}

            <div class="row col-sm-12">
                <div class="col-md-8 pull-right" >
                    <button type="submit" name="save" value="save" class="btn btn-primary">
                        Сохранить
                    </button>
                    <a href="{% url 'core_create_pcs_message' %}" class="btn btn-default">
                        <span>Отменить</span>
                    </a>
                    <a href="{% url 'core_list_pcs_messages' %}" class="btn btn-default">
                        <span>Выход</span>
                    </a>
                </div>
            </div>
        </form>
    </div>

</div>

{% endblock %}