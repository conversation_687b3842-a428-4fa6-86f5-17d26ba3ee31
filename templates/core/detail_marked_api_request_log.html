{% extends 'layout.html' %}
{% load request_view permissions secure_protocols %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Просмотр приема АПИ маркировок</h3>
        </div>

        <div class="row" style="margin-top: 20px">
            <div>
                <dl class="dl-horizontal">
                    <dt>Код пользователя API маркировки</dt>
                    <dd>
                        {% if business_entity %}
                            {{ business_entity.unp }} {{ business_entity.full_name }}
                        {% else %}
                        -
                        {% endif %}
                    </dd>
                    <dt>Тип запроса</dt>
                    <dd>{{ marked_api_request.get_request_type_display}}</dd>
                    <dt>Дата и время запроса</dt>
                    <dd>{{ marked_api_request.received_at|date:"d-m-Y G:i:s" }}</dd>
                    <dt>Заголовки запроса</dt>
                    <dd>
                        <pre id="req_headers">
                            {{ marked_api_request.request_headers|default:'' }}
                        </pre>
                    </dd>
                    <dt style="margin-right: 15px">Тело запроса</dt>
                    <pre id="req_body">{{ marked_api_request.request_body }}</pre>
                    <dt style="margin-right: 15px">Тело ответа</dt>
                    <pre id="answer_body">{{ marked_api_request.answer_body }}</pre>
                    <dt>Код ответа</dt>
                    <dd>{{ marked_api_request.answer_code }}</dd>
                    <dt>Код ошибки</dt>
                    <dd>{{ marked_api_request.get_error_code_display|default_if_none:'Успешно' }}</dd>
                </dl>
            </div>
        </div>

        <div class="row col-sm-6">
            <div class="col-md-8 pull-right" >
                <a href="{{ exit_url }}" class="btn btn-default">
                    <span>Выход</span>
                </a>
            </div>
        </div>
    </div>


    <script>

        $(document).ready(function () {
            insert_text('req_body');
            insert_text('answer_body');
            insert_text('req_headers');

            function insert_text(id_el) {
                let el = document.getElementById(id_el)
                let data = el.innerHTML
                if (!data || data === 'None') {
                    return el.innerHTML = ''
                }
                try {
                    el.innerHTML = JSON.stringify(JSON.parse(data), null, 4);
                } catch (e) {
                    console.log('JSON parse Error');
                }
            }
        });

    </script>

{% endblock %}