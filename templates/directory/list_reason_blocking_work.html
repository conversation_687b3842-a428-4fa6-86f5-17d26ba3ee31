{% extends 'layout.html' %}
{% load permissions table_sorting endless %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Основания блокирования работы КО</h3>
        </div>

        <div class="row" style="margin: 20px 0">
            {% ifperms reason_blocking_work_create %}
                <div class="col-lg-10">
                    {% include 'ww_filters/_list_forms.html' with type='reason_blocking_work' %}
                </div>
                <div class="col-lg-2">
                    <a class="btn btn-primary pull-right" href="{% url 'directory_reason_blocking_work_create' %}">
                        <span class="glyphicon glyphicon-plus"></span> Создать запись</a>
                </div>
            {% else %}
                <div class="col-lg-12">
                    {% include 'ww_filters/_list_forms.html' with type='reason_blocking_work' %}
                </div>
            {% endifperms %}
        </div>

        {% paginate object_list %}

        {% if object_list %}
            <table class="table table-striped" style="margin-top: 60px">
                <thead>
                    <tr>
                        <th {% sort_attributes params 'id' %}>Номер</th>
                        <th {% sort_attributes params 'name' %}>Наименование основания блокировки</th>
                        <th {% sort_attributes params 'description' %}>Описание</th>
                        <th {% sort_attributes params 'start_date' %}>Дата начала</th>
                        <th {% sort_attributes params 'end_date' %}>Дата окончания</th>
                        <th {% sort_attributes params 'created_at' %}>Дата создания</th>
                        <th {% sort_attributes params 'edited_at' %}>Дата редактирования</th>
                        <th style="width: 47px"></th>
                    </tr>
                </thead>
                <tbody>
                {% for ol in object_list %}
                    <tr>
                        <td>{{ ol.id }}</td>
                        <td>{{ ol.name }}</td>
                        <td>{{ ol.description|default:' ' }}</td>
                        <td>{{ ol.start_date }}</td>
                        <td>{{ ol.end_date|default:' ' }}</td>
                        <td>{{ ol.created_at|default:' ' }}</td>
                        <td>{{ ol.edited_at|default:' ' }}</td>
                        <td>
                            {% ifperms reason_blocking_work_edit %}
                                <a href="{% url 'directory_reason_blocking_work_edit' ol.pk %}"
                                   class="glyphicon glyphicon-edit"></a>
                            {% endifperms %}

                            {% ifperms reason_blocking_work_detail %}
                                <a href="{{ ol.get_absolute_url }}" class="glyphicon glyphicon-eye-open"></a>
                            {% endifperms %}
                        </td>
                    </tr>
                {% endfor %}
                </tbody>
            </table>
            {% show_pages %}
        {% else %}
            <h4 style="text-align: center">Записей нет.</h4>
        {% endif %}
    </div>
{% endblock %}