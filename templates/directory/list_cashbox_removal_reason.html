{% extends 'layout.html' %}
{% load permissions table_sorting endless %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Причины снятия кассового оборудования</h3>
        </div>

        <a class="btn btn-primary pull-right" href="{% url 'directory_create_cashbox_removal_reason' %}">
            <span class="glyphicon glyphicon-plus"></span> Создать запись
        </a>

        {% paginate object_list %}

        {% if object_list %}
            <table class="table table-striped" style="margin-top: 60px">
                <thead>
                    <tr>
                        <th {% sort_attributes params 'details' %}>Причина снятия</th>
                        <th {% sort_attributes params 'recovery' %}>Запретить постановку на учет КО</th>
                        {% ifperms removal_reason_edit or removal_reason_detail %}
                            <th style="width: 47px"></th>
                        {% endifperms %}
                    </tr>
                </thead>
                <tbody>
                    {% for ol in object_list %}
                        <tr>
                            <td>{{ ol.details }}</td>
                            <td>{% if ol.recovery %}<i class="glyphicon glyphicon-ok"></i>{% endif %}</td>
                            {% ifperms removal_reason_edit or removal_reason_detail %}
                                <td>
                                    {% ifperms removal_reason_edit %}
                                        <a href="{% url 'directory_edit_cashbox_removal_reason' ol.pk %}" class="glyphicon glyphicon-edit"></a>
                                    {% endifperms %}
                                    {% ifperms removal_reason_detail %}
                                        <a href="{{ ol.get_absolute_url }}" class="glyphicon glyphicon-eye-open"></a>
                                    {% endifperms %}
                                </td>
                            {% endifperms %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
            {% show_pages %}
        {% else %}
            <h4 style="text-align: center">Записей нет.</h4>
        {% endif %}
    </div>
{% endblock %}