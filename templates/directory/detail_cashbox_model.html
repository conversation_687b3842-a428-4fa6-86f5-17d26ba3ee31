{% extends 'layout.html' %}
{% load permissions %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Просмотр информации о модели кассового оборудования</h3>
        </div>

        <dl class="dl-horizontal">
            <dt>Классификационная группа</dt><dd>{{ object.classification_group.full_name }}</dd>
            <dt>Регистрационный номер</dt><dd>{{ object.register_number }}</dd>
            <dt>Наименование</dt><dd>{{ object.name }}</dd>
            <dt>Версия ПО</dt><dd>{{ object.software_version }}</dd>
            <dt>Сфера применения</dt><dd>{{ object.scope }}</dd>
            <dt>Номер решения</dt><dd>{{ object.decision_number }}</dd>
            <dt>Дата принятия решения</dt><dd>{{ object.decision_date|date }}</dd>
            <dt>Срок нахождения в реестре</dt><dd>{{ object.removal_date|date }}</dd>
            <dt>Реестр</dt><dd>{% if not object.in_exception_date %}Да{% else %}Нет{% endif %}</dd>
            <dt>Дата принятия решения об исключении</dt><dd>{{ object.exception_date|date }}</dd>
            <dt>Дата вступления в силу решения об исключении</dt><dd>{{ object.in_exception_date|date }}</dd>
            <dt>Соответсвие СТБ/РУП</dt><dd>{% if object.stb_conformity %}Да{% else %}Нет{% endif %}</dd>
            <dt>Дата изменения соответсвия СТБ/РУП</dt><dd>{{ object.conformity_date|date }}</dd>
            <dt>Заявитель</dt><dd>{{ object.applicant }}</dd>
            <dt>Изготовители</dt>
            <dd>
                {% for manuf in cm.manufacturers.all %}
                    {{ manuf }}
                    {% if not forloop.last %}, {% endif %}
                {% endfor %}
            </dd>
            <dt>Примечание</dt><dd>{{ object.note }}</dd>
            <dt>Соответствует требованиям 2023г.</dt><dd>{% if object.demands_2023 %}Да{% else %}Нет{% endif %}</dd>
            <dt>Может обрабатывать маркированные товары</dt><dd>{% if object.mark_ready %}Да{% else %}Нет{% endif %}</dd>
            <dt>Никогда не будет обновлен до требований 2023г</dt><dd>{% if object.never_demands_2023 %}Да{% else %}Нет{% endif %}</dd>
            <dt>Дата ввода</dt><dd>{{ object.start_date|date }}</dd>
            <dt>Дата окончания</dt><dd>{{ object.end_date|date }}</dd>
        </dl>

        <div class="row col-sm-6">
            <div class="col-md-8 pull-right" >
                {% ifperms cashbox_model_edit %}
                    <a href="{% url 'directory_cashbox_model_edit' object.pk %}" class="btn btn-default">
                        <span>Редактировать</span>
                    </a>
                {% endifperms %}
                <a href="{{ exit_url }}" class="btn btn-default">
                    <span>Выход</span>
                </a>
            </div>
        </div>
    </div>
{% endblock %}