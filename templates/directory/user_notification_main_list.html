{% extends 'layout.html' %}
{% load permissions %}

{% block content %}
    <div class="container">
        {% if notifications %}
            <div class="notification-feed">
                {% for notification in notifications %}
                    <div class="panel notification-card">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-xs-10">
                                    <h4 class="panel-title">{{ notification.title }}</h4>
                                </div>
                                <div class="col-xs-2 text-right">
                                    {% if notification.post_date %}
                                        <span class="date-label">{{ notification.post_date|date:'SHORT_DATE_FORMAT' }}</span>
                                    {% else %}
                                        <span class="date-label">{{ notification.created_at|date:'SHORT_DATE_FORMAT' }}</span>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="panel-body">
                            {% if notification.text %}
                                <div class="notification-content">
                                    {{ notification.text|linebreaksbr }}
                                </div>
                            {% endif %}
                            
                            {% if notification.image_data %}
                                <div class="notification-image text-center">
                                    <img src="data:image/jpeg;base64,{{ notification.image_base64 }}" 
                                        class="img-responsive center-block" 
                                        style="max-height: 500px; margin-top: 15px;"
                                        alt="Изображение к сообщению">
                                </div>
                            {% endif %}
                            
                            {% if notification.file_data %}
                                <div class="notification-file text-center" style="margin-top: 15px;">
                                    <a href="data:application/octet-stream;base64,{{ notification.file_base64 }}" 
                                       download="{{ notification.file_name|default:'file' }}" 
                                       class="btn btn-primary">
                                        <span class="glyphicon glyphicon-download-alt"></span> 
                                        Скачать файл{% if notification.file_name %}: {{ notification.file_name }}{% endif %}
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% else %}
        {% endif %}
    </div>

    <style>
        .notification-feed {
            margin-top: 20px;
        }
        
        .notification-card {
            margin-bottom: 25px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            transition: transform 0.2s ease;
        }
        
        .notification-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
        
        .panel-heading {
            padding: 12px 15px;
            background-color: #0066b3;
            color: white;
        }
        
        .notification-content {
            font-size: 14px;
            line-height: 1.6;
        }
        
        .date-label {
            background-color: rgba(255, 255, 255, 0.2);
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .panel-title {
            margin-top: 0;
            margin-bottom: 0;
            font-size: 16px;
            font-weight: bold;
        }
    </style>
{% endblock %} 