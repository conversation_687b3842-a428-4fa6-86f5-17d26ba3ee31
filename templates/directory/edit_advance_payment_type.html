{% extends 'layout.html' %}
{% load bootstrap_input_groups bootstrap3 permissions secure_protocols staticfiles %}

{% block js %}
    <script src="{% static "core/js/bootstrap3-typeahead.js" %}"></script>
{% endblock %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>{{ object.text }} <small>Редактирование</small></h3>
        </div>


        <form method="post" autocomplete="off" class="form-horizontal" enctype="multipart/form-data" style="margin-top: 20px">
            {% csrf_token %}
            <div class="row">
                <div class="col-md-6">

                    {% bootstrap_field form.code layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}
                    {% bootstrap_field form.text layout='horizontal' field_class='col-sm-8' label_class='col-lg-4' %}
                    {% bootstrap_field form.details layout='horizontal' field_class='col-sm-8' label_class='col-lg-4' %}
                    {% bootstrap_date_field form.start_date layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}
                    {% bootstrap_date_field form.end_date layout='horizontal' field_class='col-sm-4' label_class='col-lg-4' %}
               </div>
            </div>

            <div class="row col-sm-6">
                <div class="col-md-8 pull-right" >
                    <input type="submit" class="btn btn-primary" value="Сохранить"/>
                    <a href="{% url 'directory_edit_advance_payment_type' object.pk %}" class="btn btn-default">
                        <span>Отменить</span>
                    </a>
                    <a href="{{ exit_url }}" class="btn btn-default">
                        <span>Выход</span>
                    </a>
                    {% ifperms advance_payment_types_delete %}
                        <a href="{% url 'directory_delete_advance_payment_type' object.pk %}" onclick="return confirm('Вы действительно хотите удалить?')" class="btn btn-danger pull-right">Удалить</a>
                    {% endifperms %}
                </div>
            </div>

        </form>
    </div>

    <script>
        $(function() {
            soato_monitoring();

            $('#id_unp, #id_name').typeahead({
                source: function (query) {
                    var that = this;
                    if (query != '') {
                        var param = function() {
                            var id = $(that.$element).attr('id');
                            if (id=='id_unp') {
                                return {'unp': query}
                            }
                            return {'name': query}
                        };
                        $.get('{% url 'directory_grp_suggest' %}', param(), function(result) {
                            that.process(result);
                        });
                    }
                },
                property: 'name',
                matcher: function() {return true},
                onselect: function(value) {
                    $('#id_unp').val(value['unp']);
                    $('#id_name').val(value['name']);
                    $('#id_legal_address').val(value['address'])
                }
            });

            $('#id_soato').typeahead({
                source: function (data) {
                    var that = this;
                    $.get('{% url 'core_soato_suggest' %}', {'soato': data}, function(result) {
                        that.process(result);
                    });
                },
                property: 'name',
                matcher: function() {return true},
                updater: function(item) {
                    return item['soato']
                }
            });
        })
    </script>
{% endblock %}