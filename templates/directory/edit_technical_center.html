{% extends 'layout.html' %}
{% load bootstrap3 bootstrap_input_groups permissions staticfiles secure_protocols humanize_raw_data %}

{% block js %}
    <script src="{% static "core/select2/select2.min.js" %}"></script>
    <script src="{% static "core/select2/select2_locale_ru.js" %}"></script>
{% endblock %}

{% block css %}
    <link href="{% static "core/select2/select2.css" %}" rel="stylesheet" media="screen">
    <link href="{% static "core/select2/select2-bootstrap.css" %}" rel="stylesheet" media="screen">
{% endblock %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Редактирование технического центра</h3>
        </div>

        <form method="post" class="form-horizontal" enctype="multipart/form-data">
            {% csrf_token %}
            {% bootstrap_field form.number layout='horizontal' field_class='col-sm-1' label_class='col-lg-2' %}
            {% bootstrap_field form.name layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.responsibility_area layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.post_address layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}

            {% include 'directory/_map_for_technical.html' %}

            {% bootstrap_field form.director_full_name layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.contact_inf layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.mode layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.responsible_person layout='horizontal' field_class='col-sm-3' label_class='col-lg-2' %}
            {% bootstrap_field form.post1 layout='horizontal' field_class='col-sm-3' label_class='col-lg-2' %}
            {% bootstrap_field form.post2 layout='horizontal' field_class='col-sm-3' label_class='col-lg-2' %}

            {% if object.signature_file %}
                <div class="row" style="padding-bottom: 4px">
                    <div class="col-lg-offset-2 col-lg-10">
                        <img
                                alt="signature"
                                src="{{ object.signature_file|humanize_image_raw_data }}"
                                width="240"
                        />

                        {% ifperms signature_or_stamp_from_technical_center_delete %}
                            <a
                                    onclick="return confirm('Вы уверены?');"
                                    class='close'
                                    style="float: none"
                                    href="{% url 'directory_technical_center_signature_delete' object.pk %}"
                            >
                                &times
                            </a>
                        {% endifperms %}
                    </div>
                </div>
            {% endif %}
            {% bootstrap_field form.signature_file layout='horizontal' field_class='col-sm-3' label_class='col-lg-2' %}

            {% if object.stamp_file %}
                <div class="row" style="padding-bottom: 4px">
                    <div class="col-lg-offset-2 col-lg-10">
                        <img
                                alt="stamp"
                                src="{{ object.stamp_file|humanize_image_raw_data }}"
                                width="240"
                        />

                        {% ifperms signature_or_stamp_from_technical_center_delete %}
                            <a
                                    onclick="return confirm('Вы уверены?');"
                                    class='close'
                                    style="float: none"
                                    href="{% url 'directory_technical_center_stamp_delete' object.pk %}"
                            >
                                &times
                            </a>
                        {% endifperms %}
                    </div>
                </div>
            {% endif %}
            {% bootstrap_field form.stamp_file layout='horizontal' field_class='col-sm-3' label_class='col-lg-2' %}

            {% bootstrap_field form.note layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_date_field form.start_date layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}
            {% bootstrap_date_field form.end_date layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}

            <div class="row col-sm-6">
                <div class="col-md-8 pull-right" >
                    <input type="submit" class="btn btn-primary" value="Сохранить"/>
                    <a href="{% url 'directory_edit_technical_center' form.instance.pk %}" class="btn btn-default">
                        <span>Отменить</span>
                    </a>
                    <a href="{{ exit_url }}" class="btn btn-default">
                        <span>Выход</span>
                    </a>
{#                    {% ifperms technical_center_delete %}#}
{#                        <a href="{% url 'directory_delete_technical_center' form.instance.pk %}"#}
{#                           onclick="return confirm('Вы действительно хотите удалить?')" class="btn btn-danger pull-right">#}
{#                            Удалить#}
{#                        </a>#}
{#                    {% endifperms %}#}
                </div>
            </div>
        </form>
    </div>

    <script>
        $(document).ready(function() {
            $('#id_number').mask('0000');
            $('#id_responsibility_area').select2();
        });
    </script>
{% endblock %}