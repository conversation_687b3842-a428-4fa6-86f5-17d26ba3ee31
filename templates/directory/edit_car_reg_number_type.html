{% extends 'layout.html' %}
{% load permissions bootstrap3 bootstrap_input_groups %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Редактирование типа номера государственной регистрации</h3>
        </div>

        <form method="post" class="form-horizontal" style="margin-top: 20px">
            {% csrf_token %}
            {% bootstrap_field form.name layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.details layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_date_field form.start_date layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}
            {% bootstrap_date_field form.end_date layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}

            <div class="row col-sm-6">
                <div class="col-md-8 pull-right" >
                    <input type="submit" class="btn btn-primary" value="Сохранить"/>
                    <a href="{% url 'directory_edit_car_reg_number_type' form.instance.pk %}" class="btn btn-default">
                        <span>Отменить</span>
                    </a>
                    <a href="{{ exit_url }}" onclick="" class="btn btn-default">
                        <span>Выход</span>
                    </a>
                    {% ifperms room_type_delete %}
                        <a href="{% url 'directory_delete_car_reg_number_type' form.instance.pk %}"
                           onclick="return confirm('Вы действительно хотите удалить?')" class="btn btn-danger pull-right">
                            Удалить
                        </a>
                    {% endifperms %}
                </div>
            </div>
        </form>
    </div>
{% endblock %}