{% extends 'layout.html' %}
{% load staticfiles bootstrap3 bootstrap_input_groups %}

{% block js %}
    <script src="{% static "core/select2/select2.min.js" %}"></script>
    <script src="{% static "core/select2/select2_locale_ru.js" %}"></script>
{% endblock %}

{% block css %}
    <link href="{% static "core/select2/select2.css" %}" rel="stylesheet" media="screen">
    <link href="{% static "core/select2/select2-bootstrap.css" %}" rel="stylesheet" media="screen">
{% endblock %}

{% block content %}
    <div class="container">
        <div class="page-header">
            <h3>Создание технической службы</h3>
        </div>

        <form method="post" class="form-horizontal" enctype="multipart/form-data">
            {% csrf_token %}
            {% bootstrap_field form.technical_center layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.number layout='horizontal' field_class='col-sm-1' label_class='col-lg-2' %}
            {% bootstrap_field form.name layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.post_address layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}

            {% include 'directory/_map_for_technical.html' %}

            {% bootstrap_field form.director_full_name layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.contact_inf layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.mode layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.responsible_person layout='horizontal' field_class='col-sm-3' label_class='col-lg-2' %}
            {% bootstrap_field form.post1 layout='horizontal' field_class='col-sm-3' label_class='col-lg-2' %}
            {% bootstrap_field form.post2 layout='horizontal' field_class='col-sm-3' label_class='col-lg-2' %}
            {% bootstrap_field form.signature_file layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.stamp_file layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_field form.note layout='horizontal' field_class='col-sm-4' label_class='col-lg-2' %}
            {% bootstrap_date_field form.start_date layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}
            {% bootstrap_date_field form.end_date layout='horizontal' field_class='col-sm-2' label_class='col-lg-2' %}

            <div class="row col-sm-6">
                <div class="col-md-8 pull-right" >
                    <input type="submit" class="btn btn-primary" value="Сохранить"/>
                    <a href="{% url 'directory_create_technical_service' %}" class="btn btn-default">
                        <span>Отменить</span>
                    </a>
                    <a href="{{ exit_url }}" class="btn btn-default">
                        <span>Выход</span>
                    </a>
                </div>
            </div>
        </form>

    </div>

    <script type="text/javascript">
        $(document).ready(function() {
            $("#id_technical_center").select2();
        });
    </script>
{% endblock %}