import logging
import os
import sys

from celery import Celery
from django.conf import settings

sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'skko_web_core.settings')

celery_app = Celery()
celery_app.config_from_object('django.conf:settings')
celery_app.autodiscover_tasks(settings.INSTALLED_APPS)

# Remove kombu error messages.
logging.getLogger('kombu').setLevel(logging.CRITICAL)
