# encoding: utf-8
from __future__ import unicode_literals

from django.contrib import messages
from django.template import Template, Context

import models
import tasks
import utils
from skko_web_core.apps.check_info.infs import REPORT_DATA_CHECK_INFO
from skko_web_core.settings import UL_ID

REPORT_DATA = {
    'cb_by_type':
        ('Отчет по количеству КО в разрезе классификационных групп моделей КО '
         '({{ sign_message }})',
         tasks.count_cashbox_by_type),
    'cb_by_model':
        ('Отчет по количеству КО в разрезе моделей КО ({{ sign_message }})',
         tasks.count_cashbox_by_model),
    'cb_by_imns':
        ('Отчет по количеству КО заданной модели, в разрезе ИМНС '
         '({{ sign_message }})',
         tasks.count_cashbox_by_imns),
    'cb_by_manufacturer':
        ('Отчет по количеству КО в разрезе изготовителей КО '
         '({{ sign_message }})',
         tasks.count_cashbox_by_manufacturer),
    'cb_by_withdrawal_reason':
        ('Отчет по количеству КО в разрезе причин снятия с учета КО в СККО '
         '(формируется по ИМНС постановки на учет СПД, как плательщика)',
         tasks.count_cashbox_by_withdrawal_reason),
    'cb_be_imns':
        ('Создание отчета по количеству СПД и КО (формируется по ИМНС '
         'постановки на учет СПД, как плательщика)',
         tasks.count_be_cb_imns),
    'cb_be_territory':
        ('Создание отчета по количеству СПД и КО (формируется по'
         ' территориальному признаку)',
         tasks.count_be_cb_territory),
    'be_cb_by_techservice':
        ('Создание отчета по количеству СПД и КО в разрезе ТЦ и его ТС '
         '(формируется по ТЦ, ТС постановки на учет СПД в АИС ККО и по '
         'территориальному признаку)',
         tasks.count_be_cb_by_techservice),
    'hardware_by_tc':
        ('Отчет по комплектующим СКНО, СКЗИ, SIM (формируется по техническим'
         ' центрам)',
         tasks.count_hardware_by_tc),
    'works_by_rup_objects':
        ('Отчет по выполненным работам ТЦ (формируется по техническим центрам)',
         tasks.count_works_by_rup_objects),
    'new_cbs_by_rup_objects':
        ('Отчет по количеству поставленного на учет и подключенного КО '
         'к системе АИС ККО '
         '(формируется по техническим центрам)',
         tasks.count_new_cbs_by_rup_objects),
    'daily_be_sales_by_cb':
        ('Отчет по среднесуточным продажам (услугам) СПД в разрезе КО за месяц',
         tasks.daily_be_sales_by_cb),
    'monthly_be_sales_by_cb':
        ('Отчет по среднемесячным продажам (услугам) СПД в разрезе КО за месяц',
         tasks.monthly_be_sales_by_cb),
    'detail_be_sales_by_cb':
        ('Отчет по продажам СПД за месяц в разрезе КО (детализация по датам)',
         tasks.detail_be_sales_by_cb),
    'detail_be_sales_by_to':
        ('Отчет по продажам СПД за месяц в разрезе торговых объектов СПД '
         '(детализация по датам)',
         tasks.detail_be_sales_by_to),
    'user_by_role':
        ('Отчет по количеству пользователей по ролям',
         tasks.count_user_by_role),
    'be_cb_by_type':
        ('Отчет по количеству КО у СПД в разрезе классификационных групп '
         'моделей КО',
         tasks.count_be_cashbox_by_type),
    'be_cb_by_model':
        ('Отчет по количеству КО у СПД в разрезе моделей КО',
         tasks.count_be_cashbox_by_model),
    'cbs_on_tcs_in_month':
        ('Отчет по количеству поставленного и подключённого КО по дням',
         tasks.count_cbs_on_tcs_in_month),
    'tax_controllers_works':
        ('Отчёт о посещениях АИС ККО работниками налоговых органов',
         tasks.tax_controllers_works),
    'cashbox_shifts_count':
        ('Отчёт по количеству смен кассового оборудования ({{ sign_message }})',
         tasks.cashbox_shifts_count),
    'monthly_be_sales_for_period':
        ('Отчёт по среднемесячным продажам и выручке СПД (формируется по'
         ' ИМНС постановки на учет СПД, как плательщика)',
         tasks.monthly_be_sales_for_period),
    'be_proceeds_by_to_type':
        ('Отчёт о выручке СПД в разрезе типов ТО ({{ sign_message }})',
         tasks.be_proceeds_by_to_type),
    'summary_be_proceeds_by_to_type':
        ('Сводный отчёт о продажах и выручке СПД  в разрезе типов ТО '
         '(формируется по ИМНС постановки на учет СПД, как плательщика)',
         tasks.summary_be_proceeds_by_to_type),
    'cb_working':
        ('Отчет о работе КО за период ({{ sign_message }})',
         tasks.cb_working),
    'bad_checks':
        ('Отчет о проблемных чеках',
         tasks.bad_checks),
    'z_period':
        ('34. Отчет по данным Z-отчетов (детализация по датам)',
         tasks.z_period),

    'detail_be_sales_by_cb_full':
        ('35. Выручка по данным чеков в разрезе КО (детализация по датам)',
         tasks.detail_be_sales_by_cb_full),

    'time_and_count_by_cashbox':
        ('36. Временные и количественные  параметры по данным чеков'
         ' (детализация по датам)',
         tasks.time_and_count_by_cashbox),

    'time_and_count_by_z_report':
        ('37. Временные и количественные  параметры по данным Z-отчетов'
         ' (детализация по датам)',
         tasks.time_and_count_by_z_report),

    'discount_report':
        ('38. Сведения о кассовом оборудовании, '
         'в отношении которого предоставлена скидка на услуги',
        tasks.discount_report),

    'cashbox_by_demand_report':
        ('39. Отчет по количеству КО в разрезе моделей КО '
        '(в разрезе соответствия требованиям 2023)',
        tasks.cashbox_by_demand_report),
    }

REPORT_DATA.update(REPORT_DATA_CHECK_INFO)


def create_statistical_report(request, task_name, **kwargs):
    template_of_name, celery_task = REPORT_DATA[task_name]

    sign = kwargs.get('sign')
    if sign is None:
        name = template_of_name
    else:
        template = Template(template_of_name)
        context = Context({'sign_message': utils.SIGN_MESSAGES[sign]})
        name = template.render(context)

    # For BE tasks
    if request.account.role.method == UL_ID and 'unp' not in kwargs:
        kwargs['unp'] = request.account.unp

    task = models.UserTask.objects.create(user=request.account, name=name)
    task.name += '_' + str(task.pk)
    task.save(update_fields=['name'])
    messages.success(request, 'Задание на формирование отчёта успешно создано')

    # TODO: only for local debug, remove in prod, don't commit in git
    # celery_task(task, **kwargs)
    celery_task.delay(task, **kwargs)
