# encoding: utf-8

from __future__ import unicode_literals
from django.conf.urls import patterns, url

import views
import forms
import models
import filters
from skko_web_core.apps.core.decorators import permissions
from skko_web_core.apps.core.generic_views import GenericCreateView, \
    GenericUpdateView, GenericDeleteView, \
    GenericListView, GenericDetailView

urlpatterns = patterns(
    '',

    # Suggest
    url(
        r'^grp-suggest/$',
        views.grp_suggest,
        name='directory_grp_suggest'
    ),

    # Modal information
    url(
        r'^responsibility-area-info/$',
        views.responsibility_area_info,
        name='directory_responsibility_area_info'
    ),

    # External directory
    url(
        r'^upload-logs/$',
        permissions('directory_upload_log_detail')(
            GenericListView.as_view(
                model=models.ExternalDirectoryLoad,
                template_name='directory/upload_logs.html',
                filter_form=filters.ExternalDirectoryLoadFilterForm,
                filter_type='external_directory_load',
            )
        ),
        name='directory_upload_logs'
    ),
    url(
        r'^upload-external-directory/$',
        views.upload_external_directory,
        name='directory_upload_external_directory'
    ),

    # Cash box manufacturer
    url(
        r'^cashbox-manufacturer/list/$',
        permissions('manufacturer_list')(
            GenericListView.as_view(
                model=models.CashboxManufacturer,
                template_name='directory/list_cashbox_manufacturer.html',
                filter_form=filters.CashboxManufacturerFilterForm,
                filter_type='cashbox_manufacturer'
            )
        ),
        name='directory_cashbox_manufacturer_list'
    ),
    url(
        r'^cashbox-manufacturer/create/$',
        permissions('manufacturer_create')(
            views.CashboxManufacturerCreateView.as_view(
                model=models.CashboxManufacturer,
                template_name='directory/create_cashbox_manufacturer.html',
                form_class=forms.CreateCashboxManufacturerForm,
                success_message='Изготовитель кассового оборудования '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_cashbox_manufacturer_create'
    ),
    url(
        r'^cashbox-manufacturer/(?P<pk>\d+)/detail/$',
        permissions('manufacturer_detail')(
            GenericDetailView.as_view(
                model=models.CashboxManufacturer,
                template_name='directory/detail_cashbox_manufacturer.html',
            )
        ),
        name='directory_cashbox_manufacturer_detail'
    ),
    url(
        r'^cashbox-manufacturer/(?P<pk>\d+)/edit$',
        permissions('manufacturer_edit')(
            views.CashboxManufacturerUpdateView.as_view(
                model=models.CashboxManufacturer,
                template_name='directory/edit_cashbox_manufacturer.html',
                form_class=forms.CreateCashboxManufacturerForm,
                success_message='Изготовитель кассового оборудования '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_cashbox_manufacturer_edit'
    ),
    url(
        r'^cashbox-manufacturer-delete/(?P<pk>\d+)/$',
        permissions('manufacturer_delete')(
            GenericDeleteView.as_view(
                model=models.CashboxManufacturer,
            )
        ),
        name='directory_cashbox_manufacturer_delete'
    ),

    # Cash box applicant
    url(
        r'^cashbox-applicant/list/$',
        permissions('applicant_list')(
            GenericListView.as_view(
                model=models.ApplicantOfCashBox,
                template_name='directory/list_cashbox_applicant.html',
                filter_form=filters.ApplicantOfCashboxFilterForm,
                filter_type='applicant_of_cashbox'
            )
        ),
        name='directory_list_cashbox_applicant'
    ),
    url(
        r'^cashbox-applicant/create/$',
        permissions('applicant_create')(
            GenericCreateView.as_view(
                model=models.ApplicantOfCashBox,
                template_name='directory/create_cashbox_applicant.html',
                form_class=forms.CreateApplicantOfCashboxForm,
                success_message='Заявитель кассового оборудования '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_cashbox_applicant'
    ),
    url(
        r'^cashbox-applicant/(?P<pk>\d+)/detail/$',
        permissions('applicant_detail')(
            GenericDetailView.as_view(
                model=models.ApplicantOfCashBox,
                template_name='directory/detail_cashbox_applicant.html',
            )
        ),
        name='directory_detail_cashbox_applicant'
    ),
    url(
        r'^cashbox-applicant/(?P<pk>\d+)/edit/$',
        permissions('applicant_edit')(
            GenericUpdateView.as_view(
                model=models.ApplicantOfCashBox,
                template_name='directory/edit_cashbox_applicant.html',
                form_class=forms.EditApplicantOfCashboxForm,
                success_message='Заявитель кассового оборудования <'
                                'a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_edit_cashbox_applicant'
    ),
    url(
        r'^cashbox-applicant-delete/(?P<pk>\d+)/$',
        permissions('applicant_delete')(
            GenericDeleteView.as_view(
                model=models.ApplicantOfCashBox,
            )
        ),
        name='directory_delete_cashbox_applicant'
    ),

    #  Responsibility area
    url(
        r'^responsibility-area/list/$',
        permissions('responsibility_area_list')(
            GenericListView.as_view(
                model=models.ResponsibilityArea,
                template_name='directory/list_responsibility_area.html'
            )
        ),
        name='directory_list_responsibility_area'
    ),
    url(
        r'^responsibility-area/create/$',
        permissions('responsibility_area_create')(
            GenericCreateView.as_view(
                model=models.ResponsibilityArea,
                template_name='directory/create_responsibility_area.html',
                form_class=forms.CreateResponsibilityAreaForm,
                success_message='Зона ответственности '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлена'
            )
        ),
        name='directory_create_responsibility_area'
    ),
    url(
        r'^responsibility-area/(?P<pk>\d+)/detail/$',
        permissions('responsibility_area_detail')(
            GenericDetailView.as_view(
                model=models.ResponsibilityArea,
                template_name='directory/detail_responsibility_area.html',
            )
        ),
        name='directory_detail_responsibility_area'
    ),
    url(
        r'^responsibility-area/(?P<pk>\d+)/edit/$',
        permissions('responsibility_area_edit')(
            GenericUpdateView.as_view(
                model=models.ResponsibilityArea,
                template_name='directory/edit_responsibility_area.html',
                form_class=forms.EditResponsibilityAreaForm,
                success_message='Зона ответственности '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_edit_responsibility_area'
    ),
    url(
        r'^responsibility-area-delete/(?P<pk>\d+)/$',
        permissions('responsibility_area_delete')(
            GenericDeleteView.as_view(
                model=models.ResponsibilityArea,
            )
        ),
        name='directory_delete_responsibility_area'
    ),

    # Cash box type
    url(
        r'^cashbox-type/list/$',
        permissions('cashbox_type_list')(
            GenericListView.as_view(
                model=models.CashboxType,
                template_name='directory/list_cashbox_type.html',
            )
        ),
        name='directory_list_cashbox_types'
    ),
    url(
        r'^cashbox-types/create/$',
        permissions('cashbox_type_create')(
            GenericCreateView.as_view(
                model=models.CashboxType,
                form_class=forms.CreateCashboxTypeForm,
                template_name='directory/create_cashbox_type.html',
                success_message='Классификационная группа кассового '
                                'оборудования <a href='
                                '"{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлена'
            )
        ),
        name='directory_cashbox_type_create'
    ),
    url(
        r'^cashbox-type/(?P<pk>\d+)/detail/$',
        permissions('cashbox_type_detail')(
            GenericDetailView.as_view(
                model=models.CashboxType,
                template_name='directory/detail_cashbox_type.html',
            )
        ),
        name='directory_cashbox_type_detail'
    ),
    url(
        r'^cashbox-type/(?P<pk>\d+)/edit/$',
        permissions('cashbox_type_edit')(
            GenericUpdateView.as_view(
                model=models.CashboxType,
                form_class=forms.CreateCashboxTypeForm,
                template_name='directory/edit_cashbox_types.html',
                success_message='Классификационная группа кассового '
                                'оборудования <a href='
                                '"{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_cashbox_type_edit'
    ),
    url(
        r'^cashbox-type-delete/(?P<pk>\d+)/$',
        permissions('cashbox_type_delete')(
            GenericDeleteView.as_view(
                model=models.CashboxType,
            )
        ),
        name='directory_delete_cashbox_type'
    ),

    # Cash box model
    url(
        r'^cashbox-model/list/$',
        permissions('cashbox_model_list')(
            GenericListView.as_view(
                model=models.CashboxModel,
                template_name='directory/list_cashbox_model.html',
                filter_form=filters.CashboxModelFilterForm,
                filter_type='cashbox_model'
            )
        ),
        name='directory_cashbox_model_list'
    ),
    url(
        r'^unload-cashbox-model-list/$',
        views.unload_cashbox_model_list,
        name='directory_unload_cashbox_model_list'
    ),
    url(
        r'^cashbox-model/create/$',
        views.create_cashbox_model,
        name='directory_cashbox_model_create'
    ),
    url(
        r'^cashbox-model/(?P<pk>\d+)/detail/$',
        permissions('cashbox_model_detail')(
            GenericDetailView.as_view(
                model=models.CashboxModel,
                template_name='directory/detail_cashbox_model.html',
            )
        ),
        name='directory_cashbox_model_detail'
    ),
    url(
        r'^cashbox-model/(?P<pk>\d+)/edit/$',
        views.edit_cashbox_model,
        name='directory_cashbox_model_edit'
    ),
    url(
        r'^cashbox-model-delete/(?P<pk>\d+)/$',
        permissions('cashbox_model_delete')(
            GenericDeleteView.as_view(
                model=models.CashboxModel,
            )
        ),
        name='directory_cashbox_model_delete'
    ),

    # Cashbox removal reason
    url(
        r'^cashbox-removal-reason/list/$',
        permissions('removal_reason_list')(
            GenericListView.as_view(
                model=models.CashboxRemoval,
                template_name='directory/list_cashbox_removal_reason.html'
            )
        ),
        name='directory_list_cashbox_removal_reason'
    ),
    url(
        r'^cashbox-removal-reason/create/$',
        permissions('removal_reason_create')(
            GenericCreateView.as_view(
                model=models.CashboxRemoval,
                template_name='directory/create_cashbox_removal_reason.html',
                form_class=forms.CreateCashboxRemovalReasonsFrom,
                success_message='Причина удаления кассового оборудования '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлена'
            )
        ),
        name='directory_create_cashbox_removal_reason'
    ),
    url(
        r'^cashbox-removal-reason/(?P<pk>\d+)/detail/$',
        permissions('removal_reason_detail')(
            GenericDetailView.as_view(
                model=models.CashboxRemoval,
                template_name='directory/detail_cashbox_removal_reason.html',
            )
        ),
        name='directory_detail_cashbox_removal_reason'
    ),
    url(
        r'^cashbox-removal-reason/(?P<pk>\d+)/edit/$',
        permissions('removal_reason_edit')(
            GenericUpdateView.as_view(
                model=models.CashboxRemoval,
                template_name='directory/edit_cashbox_removal_reason.html',
                form_class=forms.CreateCashboxRemovalReasonsFrom,
                success_message='Причина удаления кассового оборудования '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_edit_cashbox_removal_reason'
    ),
    url(
        r'^cashbox-removal-reason-delete(?P<pk>\d+)/$',
        permissions('removal_reason_delete')(
            GenericDeleteView.as_view(
                model=models.CashboxRemoval,
            )
        ),
        name='directory_delete_cashbox_removal_reason'
    ),

    # Organizational legal form
    url(
        r'^organizational-legal-form/list/$',
        permissions('legal_form_list')(
            GenericListView.as_view(
                model=models.OrganizationalLegalForm,
                template_name='directory/list_organizational_legal_form.html',
                filter_form=filters.OrganizationalLegalFormFilterForm,
                filter_type='organizational_legal_form'
            )
        ),
        name='directory_list_organizational_legal_form'
    ),
    url(
        r'^organizational-legal-form/create/$',
        permissions('legal_form_create')(
            GenericCreateView.as_view(
                model=models.OrganizationalLegalForm,
                template_name='directory/create_organizational_legal_form.html',
                form_class=forms.CreateOrganizationalLegalForm,
                success_message='Организационно-правовая форма '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлена'
            )
        ),
        name='directory_create_organizational_legal_form'
    ),
    url(
        r'^organizational-legal-form/(?P<pk>\d+)/detail/$',
        permissions('legal_form_detail')(
            GenericDetailView.as_view(
                model=models.OrganizationalLegalForm,
                template_name='directory/detail_organizational_legal_form.html',
            )
        ),
        name='directory_detail_organizational_legal_form'
    ),
    url(
        r'^organizational-legal-form/(?P<pk>\d+)/edit/$',
        permissions('legal_form_edit')(
            GenericUpdateView.as_view(
                model=models.OrganizationalLegalForm,
                template_name='directory/edit_organizational_legal_form.html',
                form_class=forms.CreateOrganizationalLegalForm,
                success_message='Организационно-правовая форма '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_edit_organizational_legal_form'
    ),
    url(
        r'^organizational-legal-form-delete/(?P<pk>\d+)/$',
        permissions('legal_form_delete')(
            GenericDeleteView.as_view(
                model=models.OrganizationalLegalForm,
            )
        ),
        name='directory_delete_organizational_legal_form'
    ),

    # Country
    url(
        r'^country/list/$',
        permissions('country_list')(
            GenericListView.as_view(
                model=models.Country,
                template_name='directory/list_country.html',
                filter_form=filters.CountryFilterForm,
                filter_type='country'
            )
        ),
        name='directory_list_country'
    ),
    url(
        r'^country/create/$',
        permissions('country_create')(
            GenericCreateView.as_view(
                model=models.Country,
                template_name='directory/create_country.html',
                form_class=forms.CreateCountry,
                success_message='Страна <a href='
                                '"{{ object.get_absolute_url }}">{{ object }}'
                                '</a> успешно добавлена'
            )
        ),
        name='directory_create_country'
    ),
    url(
        r'^country/(?P<pk>\d+)/detail/$',
        permissions('country_detail')(
            GenericDetailView.as_view(
                model=models.Country,
                template_name='directory/detail_country.html',
            )
        ),
        name='directory_detail_country'
    ),
    url(
        r'^country/(?P<pk>\d+)/edit/$',
        permissions('country_edit')(
            GenericUpdateView.as_view(
                model=models.Country,
                template_name='directory/edit_country.html',
                form_class=forms.CreateCountry,
                success_message='Страна <a href='
                                '"{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_edit_country'
    ),
    url(
        r'^delete-country/(?P<pk>\d+)/$',
        permissions('country_delete')(
            GenericDeleteView.as_view(
                model=models.Country,
            )
        ),
        name='directory_delete_country'
    ),

    # Application scope of cash box
    url(
        r'application-scope-of-cash-box/list/$',
        permissions('scope_list')(
            GenericListView.as_view(
                model=models.ApplicationScopeOfCashBox,
                template_name='directory/list_application_scope_of_cash'
                              '_box.html'
            )
        ),
        name='directory_list_application_scope_of_cash_box'
    ),
    url(
        r'^application-scope-of-cash-box/create/$',
        permissions('scope_create')(
            GenericCreateView.as_view(
                model=models.ApplicationScopeOfCashBox,
                template_name='directory/create_application_scope_of_cash'
                              '_box.html',
                form_class=forms.CreateApplicationScopeOfCashBoxForm,
                success_message='Сфера применения кассового оборудования '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлена'
            )
        ),
        name='directory_create_application_scope_of_cash_box'
    ),
    url(
        r'^application-scope-of-cash-box/(?P<pk>\d+)/detail/$',
        permissions('scope_detail')(
            GenericDetailView.as_view(
                model=models.ApplicationScopeOfCashBox,
                template_name='directory/detail_application_scope_of_cash'
                              '_box.html',
            )
        ),
        name='directory_detail_application_scope_of_cash_box'
    ),
    url(
        r'^application-scope-of-cash-box/(?P<pk>\d+)/edit/$',
        permissions('scope_edit')(
            GenericUpdateView.as_view(
                model=models.ApplicationScopeOfCashBox,
                template_name='directory/edit_application_scope_of_cash'
                              '_box.html',
                form_class=forms.CreateApplicationScopeOfCashBoxForm,
                success_message='Сфера применения кассового оборудования '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_edit_application_scope_of_cash_box'
    ),
    url(
        r'^application-scope-of-cash-box-delete(?P<pk>\d+)/$',
        permissions('scope_delete')(
            GenericDeleteView.as_view(
                model=models.ApplicationScopeOfCashBox,
            )
        ),
        name='directory_delete_application_scope_of_cash_box'
    ),

    # Contract type
    url(
        r'^contract-type/list/$',
        permissions('contract_type_list')(
            GenericListView.as_view(
                model=models.ContractType,
                template_name='directory/list_contract_type.html'
            )
        ),
        name='directory_list_contract_type'
    ),

    url(
        r'^contract-type/create/$',
        permissions('contract_type_create')(
            GenericCreateView.as_view(
                model=models.ContractType,
                template_name='directory/create_contract_type.html',
                form_class=forms.CreateContractTypeForm,
                success_message='Тип договора '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_contract_type'
    ),
    url(
        r'^contract-type/(?P<pk>\d+)/detail/$',
        permissions('contract_type_detail')(
            GenericDetailView.as_view(
                model=models.ContractType,
                template_name='directory/detail_contract_type.html',
            )
        ),
        name='directory_detail_contract_type'
    ),
    url(
        r'^contract-type/(?P<pk>\d+)/edit/$',
        permissions('contract_type_edit')(
            GenericUpdateView.as_view(
                model=models.ContractType,
                template_name='directory/edit_contract_type.html',
                form_class=forms.CreateContractTypeForm,
                success_message='Тип договора <a href='
                                '"{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_edit_contract_type'
    ),
    url(
        r'^contract-type-delete(?P<pk>\d+)/$',
        permissions('contract_type_delete')(
            GenericDeleteView.as_view(
                model=models.ContractType,
            )
        ),
        name='directory_delete_contract_type'
    ),

    # Room type
    url(
        r'^room-type/list/$',
        permissions('room_type_list')(
            GenericListView.as_view(
                model=models.RoomType,
                template_name='directory/list_room_type.html'
            )
        ),
        name='directory_list_room_type'
    ),
    url(
        r'^room-type/create/$',
        permissions('room_type_create')(
            GenericCreateView.as_view(
                model=models.RoomType,
                template_name='directory/create_room_type.html',
                form_class=forms.CreateRoomTypeForm,
                success_message='Тип помещений '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_room_type'
    ),
    url(
        r'^room-type/(?P<pk>\d+)/detail/$',
        permissions('room_type_detail')(
            GenericDetailView.as_view(
                model=models.RoomType,
                template_name='directory/detail_room_type.html',
            )
        ),
        name='directory_detail_room_type'
    ),
    url(
        r'^room-type/(?P<pk>\d+)/edit/$',
        permissions('room_type_edit')(
            GenericUpdateView.as_view(
                model=models.RoomType,
                template_name='directory/edit_room_type.html',
                form_class=forms.CreateRoomTypeForm,
                success_message='Тип помещений '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_edit_room_type'
    ),
    url(
        r'^room-type-delete/(?P<pk>\d+)/$',
        permissions('room_type_delete')(
            GenericDeleteView.as_view(
                model=models.RoomType,
            )
        ),
        name='directory_delete_room_type'
    ),

    # Trading object type
    url(
        r'^trading-object-type/list/$',
        permissions('trading_object_type_list')(
            GenericListView.as_view(
                model=models.TradingObjectTypes,
                template_name='directory/list_trading_object_type.html',
                filter_form=filters.TradingObjectTypesFilterForm,
                filter_type='trading_object_type'
            )
        ),
        name='directory_trading_object_type_list'
    ),
    url(
        r'^trading-object-type/create/$',
        permissions('trading_object_type_create')(
            GenericCreateView.as_view(
                model=models.TradingObjectTypes,
                form_class=forms.CreateTradingObjectTypes,
                template_name='directory/create_trading_object_type.html',
                success_message='Тип торговых объектов '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_trading_object_type_create'
    ),
    url(
        r'^trading-object-type/(?P<pk>\d+)/detail/$',
        permissions('trading_object_type_detail')(
            GenericDetailView.as_view(
                model=models.TradingObjectTypes,
                template_name='directory/detail_trading_object_type.html',
            )
        ),
        name='directory_trading_object_type_detail'
    ),
    url(
        r'^trading-object-type/(?P<pk>\d+)/edit/$',
        permissions('trading_object_type_edit')(
            GenericUpdateView.as_view(
                model=models.TradingObjectTypes,
                form_class=forms.CreateTradingObjectTypes,
                template_name='directory/edit_trading_object_type.html',
                success_message='Тип торговых объектов '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_trading_object_type_edit'
    ),
    url(
        r'^trading-object-type-delete/(?P<pk>\d+)/$',
        permissions('trading_object_type_delete')(
            GenericDeleteView.as_view(
                model=models.TradingObjectTypes,
            )
        ),
        name='directory_trading_object_type_delete'
    ),

    #  Trading object groups
    url(
        r'^trading-object-group/list/$',
        permissions('trading_object_group_list')(
            GenericListView.as_view(
                model=models.TradingObjectTypesGroups,
                template_name='directory/list_trading_object_groups.html',
                filter_form=filters.TradingObjectGroupsFilterForm,
                filter_type='trading_object_group')
        ),
        name='directory_trading_object_group_list'
    ),
    url(
        r'^trading-object-group/create/$',
        permissions('trading_object_group_create')(
            GenericCreateView.as_view(
                model=models.TradingObjectTypesGroups,
                form_class=forms.CreateTradingObjectGroups,
                template_name='directory/create_trading_object_group.html',
                success_message='Группа типов торговых объектов <a href='
                                '"{% url \'directory_trading_object_group_'
                                'detail\' object.pk %}">{{ object }}</a> '
                                'успешна добавлена')
        ),
        name='directory_trading_object_group_create'
    ),
    url(
        r'^trading-object-group/(?P<pk>\d+)/detail/$',
        permissions('trading_object_group_detail')(
            GenericDetailView.as_view(
                model=models.TradingObjectTypesGroups,
                template_name='directory/detail_trading_object_group.html')
        ),
        name='directory_trading_object_group_detail'
    ),
    url(
        r'^trading-object-group/(?P<pk>\d+)/edit/$',
        permissions('trading_object_group_edit')(
            GenericUpdateView.as_view(
                model=models.TradingObjectTypesGroups,
                form_class=forms.CreateTradingObjectGroups,
                template_name='directory/edit_trading_object_group.html',
                success_message='Группа типов торговых объектов <a href='
                                '"{% url \'directory_trading_object_group_'
                                'detail\' object.pk %}">{{ object }}</a> '
                                'успешна изменена')
        ),
        name='directory_trading_object_group_edit'
    ),
    url(
        r'^trading-object-group-delete/(?P<pk>\d+)/$',
        permissions('trading_object_group_delete')(
            GenericDeleteView.as_view(
                model=models.TradingObjectTypesGroups, )
        ),
        name='directory_trading_object_group_delete'
    ),

    # Service centre
    url(
        r'^service-centre/list/$',
        permissions('service_centre_list')(
            GenericListView.as_view(
                model=models.ServiceCentre,
                template_name='directory/list_service_centre.html',
                filter_form=filters.ServiceCentreFilterForm,
                filter_type='service_centre'
            )
        ),
        name='directory_list_service_centre'
    ),
    url(
        r'^service-centre/create/$',
        views.create_service_centre,
        name='directory_create_service_centre'
    ),
    url(
        r'^sevrice-centre/(?P<pk>\d+)/detail/$',
        permissions('service_centre_detail')(
            GenericDetailView.as_view(
                model=models.ServiceCentre,
                template_name='directory/detail_service_centre.html',
            )
        ),
        name='directory_detail_service_centre'
    ),
    url(
        r'^service-centre/(?P<pk>\d+)/edit/$',
        views.edit_service_centre,
        name='directory_edit_service_centre'
    ),
    url(
        r'^service-centre-delete/(?P<pk>\d+)/$',
        permissions('service_centre_delete')(
            GenericDeleteView.as_view(
                model=models.ServiceCentre,
            )
        ),
        name='directory_delete_service_centre'
    ),
    url(
        r'^certificate-delete/(?P<pk>\d+)/$',
        views.certificate_delete,
        name='certificate_delete'
    ),

    # Service centre affiliate
    url(
        r'^sevrice-centre/(?P<pk>\d+)/affiliate/list/$',
        views.list_service_centre_affiliate,
        name='directory_list_service_centre_affiliate'
    ),
    url(
        r'^service-centre/(?P<pk>\d+)/affiliate/create/$',
        views.create_service_centre_affiliate,
        name='directory_create_service_centre_affiliate'
    ),
    url(
        r'^service-centre/affiliate/(?P<pk>\d+)/detail/$',
        views.detail_service_centre_affiliate,
        name='directory_detail_service_centre_affiliate'
    ),
    url(
        r'^service-centre/affiliate/(?P<pk>\d+)/edit/$',
        views.edit_service_centre_affiliate,
        name='directory_edit_service_centre_affiliate'
    ),
    url(
        r'^service-centre/affiliate-delete/(?P<pk>\d+)/$',
        views.delete_service_centre_affiliate,
        name='directory_delete_service_centre_affiliate'
    ),
    url(
        r'^affiliate-certificate-delete/(?P<pk>\d+)/$',
        views.delete_affiliate_certificate,
        name='directory_delete_affiliate_certificate'
    ),

    # Technical service
    url(
        r'technical-service/list/$',
        permissions('technical_service_list')(
            GenericListView.as_view(
                queryset=models.TechnicalServiceOrCenter.objects
                .filter(technical_center__isnull=False),
                template_name='directory/list_technical_service.html',
            )
        ),
        name='directory_list_technical_service'
    ),
    url(
        r'^technical-service/create/$',
        permissions('technical_service_create')(
            GenericCreateView.as_view(
                model=models.TechnicalServiceOrCenter,
                template_name='directory/create_technical_service.html',
                form_class=forms.CreateTechnicalServiceForm,
                success_message='Техническая служба '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_technical_service'
    ),
    url(
        r'^technical-service/(?P<pk>\d+)/edit/$',
        permissions('technical_service_edit')(
            GenericUpdateView.as_view(
                model=models.TechnicalServiceOrCenter,
                template_name='directory/edit_technical_service.html',
                form_class=forms.EditTechnicalServiceForm,
                success_message='Техническая служба '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_edit_technical_service'
    ),
    url(
        r'^technical-service/(?P<pk>\d+)/detail/$',
        permissions('technical_service_detail')(
            GenericDetailView.as_view(
                model=models.TechnicalServiceOrCenter,
                template_name='directory/detail_technical_service.html',
            )
        ),
        name='directory_detail_technical_service'
    ),
    # url(r'^technical-service/(?P<pk>\d+)/delete/$',
    #     permissions('technical_service_delete')(
    #         GenericDeleteView.as_view(
    #             model=models.TechnicalServiceOrCenter,
    #         )
    #     ),
    #     name='directory_delete_technical_service'
    # ),
    url(
        r'^technical-service/(?P<pk>\d+)/signature/delete/$',
        views.technical_service_signature_delete,
        name='directory_technical_service_signature_delete'
    ),
    url(
        r'^technical-service/(?P<pk>\d+)/stamp/delete/$',
        views.technical_service_stamp_delete,
        name='directory_technical_service_stamp_delete'
    ),

    # Technical center
    url(
        r'technical-center/list/$',
        permissions('technical_center_list')(
            GenericListView.as_view(
                queryset=models.TechnicalServiceOrCenter.objects
                .filter(technical_center__isnull=True),
                template_name='directory/list_technical_center.html',
            )
        ),
        name='directory_list_technical_center'
    ),
    url(
        r'^technical-center/create/$',
        permissions('technical_center_create')(
            GenericCreateView.as_view(
                model=models.TechnicalServiceOrCenter,
                template_name='directory/create_technical_center.html',
                form_class=forms.CreateTechnicalCenterForm,
                success_message='Технический центр '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_technical_center'
    ),
    url(
        r'^technical-center/(?P<pk>\d+)/detail/$',
        permissions('technical_center_detail')(
            GenericDetailView.as_view(
                model=models.TechnicalServiceOrCenter,
                template_name='directory/detail_technical_center.html',
            )
        ),
        name='directory_detail_technical_center'
    ),
    url(
        r'^technical-center/(?P<pk>\d+)/edit/$',
        permissions('technical_center_edit')(
            GenericUpdateView.as_view(
                model=models.TechnicalServiceOrCenter,
                template_name='directory/edit_technical_center.html',
                form_class=forms.EditTechnicalCenterForm,
                success_message='Технический центр '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_edit_technical_center'
    ),
    # url(r'^technical-center-delete(?P<pk>\d+)/$',
    #     permissions('technical_center_delete')(
    #         GenericDeleteView.as_view(
    #             model=models.TechnicalServiceOrCenter,
    #         )
    #     ),
    #     name='directory_delete_technical_center'
    # ),
    url(
        r'^technical-center/(?P<pk>\d+)/signature/delete/$',
        views.technical_center_signature_delete,
        name='directory_technical_center_signature_delete'
    ),
    url(
        r'^technical-center/(?P<pk>\d+)/stamp/delete/$',
        views.technical_center_stamp_delete,
        name='directory_technical_center_stamp_delete'
    ),

    # GRP
    url(
        r'^grp/list/$',
        permissions('dir_grp_list')(
            GenericListView.as_view(
                model=models.GRP,
                template_name='directory/list_grp.html',
                filter_form=filters.GRPFilterForm,
                filter_type='grp'
            )
        ),
        name='directory_list_grp'
    ),
    url(
        r'^grp_download/$',
        views.grp_download,
        name='directory_grp_download'
    ),
    url(
        r'^grp/(?P<pk>\d+)/detail/$',
        permissions('grp_detail')(
            GenericDetailView.as_view(
                model=models.GRP,
                template_name='directory/detail_grp.html',
            )
        ),
        name='directory_detail_grp'
    ),

    # Economic activity type
    url(
        r'^activity_type/list/$',
        permissions('dir_activity_type')(
            GenericListView.as_view(
                model=models.ActivityType,
                template_name='directory/list_activity_type.html',
                filter_form=filters.ActivityTypeFilterForm,
                filter_type='activity_type'
            )
        ),
        name='directory_list_activity_type'
    ),

    # Street
    url(
        r'^street/list/$',
        permissions('dir_street_list')(
            GenericListView.as_view(
                model=models.Street,
                queryset=models.Street.objects \
                    .order_by('soato', 'type', 'name'),
                template_name='directory/list_street.html',
                filter_form=filters.StreetFilterForm,
                filter_type='street'
            )
        ),
        name='directory_list_street'
    ),
    url(
        r'^street/(?P<pk>\d+)/detail/$',
        permissions('street_detail')(
            GenericDetailView.as_view(
                model=models.Street,
                template_name='directory/detail_street.html',
            )
        ),
        name='directory_detail_street'
    ),

    # SOATO
    url(
        r'^soato/list/$',
        permissions('dir_soato_list')(
            GenericListView.as_view(
                model=models.SOATO,
                template_name='directory/list_soato.html',
                filter_form=filters.SOATOFilterForm,
                filter_type='soato'
            )
        ),
        name='directory_list_soato'
    ),
    url(
        r'^soato/(?P<pk>\d+)/detail/$',
        permissions('soato_detail')(
            GenericDetailView.as_view(
                model=models.SOATO,
                template_name='directory/detail_soato.html',
            )
        ),
        name='directory_detail_soato'
    ),

    # Currency
    url(
        r'^currency/list/$',
        permissions('dir_currency_list')(
            GenericListView.as_view(
                model=models.Currency,
                queryset=models.Currency.objects.order_by('-datezk', 'kiso'),
                template_name='directory/list_currency.html',
                filter_form=filters.CurrencyFilterForm,
                filter_type='currency'
            )
        ),
        name='directory_list_currency'
    ),
    url(
        r'^currency/(?P<pk>\d+)/detail/$',
        permissions('currency_detail')(
            GenericDetailView.as_view(
                model=models.Currency,
                template_name='directory/detail_currency.html',
            )
        ),
        name='directory_detail_currency'
    ),

    # Rate
    url(
        r'^rate/list/$',
        permissions('dir_rate_list')(
            GenericListView.as_view(
                model=models.Rate,
                queryset=models.Rate.objects.order_by('-date', 'kiso'),
                template_name='directory/list_rate.html',
                filter_form=filters.RateFilterForm,
                filter_type='rate'
            )
        ),
        name='directory_list_rate'
    ),
    url(
        r'^rate/(?P<pk>\d+)/detail/$',
        permissions('rate_detail')(
            GenericDetailView.as_view(
                model=models.Rate,
                template_name='directory/detail_rate.html',
            )
        ),
        name='directory_detail_rate'
    ),

    # IMNS
    url(
        r'imns/list/$',
        permissions('imns_list')(
            GenericListView.as_view(
                model=models.Mns,
                template_name='directory/list_imns.html',
                filter_form=filters.MNSFilterForm,
                filter_type='mns'
            )
        ),
        name='directory_list_imns'
    ),
    url(
        r'^imns/(?P<pk>\d+)/detail/$',
        permissions('imns_detail')(
            GenericDetailView.as_view(
                model=models.Mns,
                template_name='directory/detail_imns.html',
            )
        ),
        name='directory_detail_imns'
    ),

    # SupervisoryAuthority
    url(
        r'supervisory-authority/list/$',
        permissions('supervisory_authority_list')(
            GenericListView.as_view(
                model=models.SupervisoryAuthority,
                template_name='directory/list_supervisory_authority.html',
                filter_form=filters.SupervisoryAuthorityFilterForm,
                filter_type='supervisory_authority'
            )
        ),
        name='directory_list_supervisory_authority'
    ),
    url(
        r'^supervisory-authority/create/$',
        permissions('supervisory_authority_create')(
            GenericCreateView.as_view(
                model=models.SupervisoryAuthority,
                template_name='directory/create_supervisory_authority.html',
                form_class=forms.CreateSupervisoryAuthorityForm,
                success_message='Контролирующий орган '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_supervisory_authority'
    ),
    url(
        r'^supervisory-authority/edit/(?P<pk>\d+)/$',
        permissions('supervisory_authority_edit')(
            GenericUpdateView.as_view(
                model=models.SupervisoryAuthority,
                template_name='directory/edit_supervisory_authority.html',
                form_class=forms.EditSupervisoryAuthorityForm,
                success_message='Контролирующий орган '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно отредактирован'
            )
        ),
        name='directory_edit_supervisory_authority'
    ),
    url(
        r'^supervisory-authority/detail/(?P<pk>\d+)/$',
        permissions('supervisory_authority_detail')(
            GenericDetailView.as_view(
                model=models.SupervisoryAuthority,
                template_name='directory/detail_supervisor_authority.html',
            )
        ),
        name='directory_detail_supervisory_authority'
    ),
    url(
        r'^supervisory-authority/delete/(?P<pk>\d+)/$',
        permissions('supervisory_authority_delete')(
            GenericDeleteView.as_view(
                model=models.SupervisoryAuthority,
            )
        ),
        name='directory_delete_supervisory_authority'
    ),

    # SupervisorySubdvision
    url(
        r'supervisory-subdivision/list/(?P<pk>\d+)/$',
        views.list_supervisory_subdivision,
        name='directory_list_supervisory_subdivision'
    ),
    url(
        r'^supervisory-subdivision/create/(?P<pk>\d+)/$',
        views.create_supervisory_subdivision,
        name='directory_create_supervisory_subdivision'
    ),

    url(
        r'^supervisory-subdivision/edit/(?P<pk>\d+)/$',
        permissions('supervisory_subdivision_edit')
        (views.edit_supervisory_subdivision),
        name='directory_edit_supervisory_subdivision'
    ),

    url(
        r'^supervisory-subvdiision/detail/(?P<pk>\d+)/$',
        permissions('supervisory_subdivision_detail')(
            GenericDetailView.as_view(
                model=models.SupervisorySubdivision,
                template_name='directory/detail_supervisory_subdivision.html',
            )
        ),
        name='directory_detail_supervisory_subdivision'
    ),
    # url(
    #     r'^supervisory-subdivision/delete/(?P<pk>\d+)/$',
    #     views.delete_supervisory_subdivision,
    #     name='directory_delete_supervisory_subdivision'
    # ),

    # Payment
    url(
        r'^payment/list/$',
        permissions('payment_list')(
            GenericListView.as_view(
                model=models.Payment,
                queryset=models.Payment.objects.order_by('-payment_start_date'),
                template_name='directory/list_payment.html',
                filter_form=filters.PaymentFilterForm,
                filter_type='payment',
            )
        ),
        name='directory_list_payment'
    ),
    url(
        r'^payment/create/$',
        permissions('payment_create')(
            GenericCreateView.as_view(
                model=models.Payment,
                template_name='directory/create_payment.html',
                form_class=forms.CreatePaymentForm,
                success_message='Оплата <a href="{{ object.get_absolute_url }}'
                                '">{{ object }}</a> успешно добавлена'
            )
        ),
        name='directory_create_payment'
    ),
    url(
        r'^payment/(?P<pk>\d+)/detail/$',
        permissions('payment_detail')(
            GenericDetailView.as_view(
                model=models.Payment,
                template_name='directory/detail_payment.html',
            )
        ),
        name='directory_detail_payment'
    ),
    url(
        r'^payment/(?P<pk>\d+)/edit/$',
        permissions('payment_edit')(
            GenericUpdateView.as_view(
                model=models.Payment,
                template_name='directory/edit_payment.html',
                form_class=forms.EditPaymentForm,
                success_message='Оплата <a href="{{ object.get_absolute_url }}'
                                '">{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_edit_payment'
    ),
    url(
        r'^payment/(?P<pk>\d+)/delete/$',
        permissions('payment_delete')(
            GenericDeleteView.as_view(
                model=models.Payment,
            )
        ),
        name='directory_delete_payment'
    ),

    # _________________________________ Акции ________________________________

    url(
        r'^discount/list/$',
        permissions('discount_lst')(
            GenericListView.as_view(
                model=models.Discount,
                queryset=models.Discount.objects.order_by('-id'),
                template_name='directory/list_discount.html',
                filter_form=filters.DiscountFilterForm,
                filter_type='discount',
            )
        ),
        name='directory_list_discount'
    ),
    url(
        r'^discount/create/$',
        permissions('discount_create')(
            GenericCreateView.as_view(
                model=models.Discount,
                template_name='directory/create_discount.html',
                form_class=forms.CreateDiscountForm,
                success_message='Акция <a href="{{ object.get_absolute_url }}"'
                                '>{{ object }}</a> успешно добавлена'
            )
        ),
        name='directory_create_discount'
    ),
    url(
        r'^discount/(?P<pk>\d+)/edit/$',
        permissions('discount_edit')(
            GenericUpdateView.as_view(
                model=models.Discount,
                template_name='directory/edit_discount.html',
                form_class=forms.EditDiscountForm,
                success_message='Акция <a href="{{ object.get_absolute_url }}"'
                                '>{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_edit_discount'
    ),
    url(
        r'^discount/(?P<pk>\d+)/delete/$',
        permissions('discount_delete')(
            GenericDeleteView.as_view(
                model=models.Discount,
            )
        ),
        name='directory_delete_discount'
    ),
    url(
        r'^discount/(?P<pk>\d+)/detail/$',
        permissions('discount_detail')(
            GenericDetailView.as_view(
                model=models.Discount,
                template_name='directory/detail_discount.html',
            )
        ),
        name='directory_detail_discount'
    ),

    #  ___________________________ Производители СКО ___________________________

    url(
        r'^sko-manufacturer/list/$',
        permissions('sko_manufacturer_list')(
            GenericListView.as_view(
                model=models.SKO_Manufacturers,
                template_name='directory/list_sko_manufacturers.html',
                filter_form=filters.SKOManufacturersFilterForm,
                filter_type='sko_manufacturers'
            )
        ),
        name='directory_list_sko_manufacturers'
    ),
    url(
        r'^sko-manufacturer/(?P<pk>\d+)/detail/$',
        permissions('sko_manufacturer_detail')(
            GenericDetailView.as_view(
                model=models.SKO_Manufacturers,
                template_name='directory/detail_sko_manufacturers.html',
            )
        ),
        name='directory_detail_sko_manufacturers'
    ),
    url(
        r'^sko-manufacturer/create/$',
        permissions('sko_manufacturer_create')(
            GenericCreateView.as_view(
                model=models.SKO_Manufacturers,
                form_class=forms.CreateSKOManufacturer,
                template_name='directory/create_sko_manufacturers.html',
                success_message='Производитель СКО '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_sko_manufacturers'
    ),
    url(
        r'^sko-manufacturer/(?P<pk>\d+)/edit/$',
        permissions('sko_manufacturer_edit')(
            GenericUpdateView.as_view(
                model=models.SKO_Manufacturers,
                form_class=forms.EditSKOManufacturer,
                template_name='directory/edit_sko_manufacturers.html',
                success_message='Производитель СКО '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_edit_sko_manufacturers'
    ),
    url(
        r'^sko-manufacturer-delete/(?P<pk>\d+)/$',
        permissions('sko_manufacturer_delete')(
            GenericDeleteView.as_view(
                model=models.SKO_Manufacturers,
            )
        ),
        name='directory_delete_sko_manufacturers'
    ),

    # ________________________________ Модели СКО ______________________________

    url(
        r'^sko-model/list/$',
        permissions('sko_model_list')(
            GenericListView.as_view(
                model=models.SKO_Models,
                template_name='directory/list_sko_model.html',
                filter_form=filters.SKOModelsFilterForm,
                filter_type='sko_models'
            )
        ),
        name='directory_list_sko_models'
    ),
    url(
        r'^sko-model/(?P<pk>\d+)/detail/$',
        permissions('sko_model_detail')(
            GenericDetailView.as_view(
                model=models.SKO_Models,
                template_name='directory/detail_sko_model.html',
            )
        ),
        name='directory_detail_sko_models'
    ),
    url(
        r'^sko-model/create/$',
        permissions('sko_model_create')(
            GenericCreateView.as_view(
                model=models.SKO_Models,
                form_class=forms.CreateSKOModel,
                template_name='directory/create_sko_model.html',
                success_message='Модель СКО '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_sko_models'
    ),
    url(
        r'^sko-model/(?P<pk>\d+)/edit/$',
        permissions('sko_model_edit')(
            GenericUpdateView.as_view(
                model=models.SKO_Models,
                form_class=forms.EditSKOModel,
                template_name='directory/edit_sko_model.html',
                success_message='Модель СКО '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_edit_sko_models'
    ),
    url(
        r'^sko-model-delete/(?P<pk>\d+)/$',
        permissions('sko_model_delete')(
            GenericDeleteView.as_view(
                model=models.SKO_Models,
            )
        ),
        name='directory_delete_sko_models'
    ),

    # _________________________ Виды авансовых платежей ________________________

    url(
        r'^advance-payment-types/list/$',
        permissions('advance_payment_types_list')(
            GenericListView.as_view(
                model=models.AdvancePaymentTypes,
                template_name='directory/list_advance_payment_type.html',
                filter_form=filters.AdvancePaymentTypesFilterForm,
                filter_type='advance_payment_types'
            )
        ),
        name='directory_list_advance_payment_types'
    ),
    url(
        r'^advance-payment-types/(?P<pk>\d+)/detail/$',
        permissions('advance_payment_types_detail')(
            GenericDetailView.as_view(
                model=models.AdvancePaymentTypes,
                template_name='directory/detail_advance_payment_type.html',
            )
        ),
        name='directory_detail_advance_payment_type'
    ),
    url(
        r'^advance-payment-types/create/$',
        permissions('advance_payment_types_create')(
            GenericCreateView.as_view(
                model=models.AdvancePaymentTypes,
                form_class=forms.CreateAdvancePaymentType,
                template_name='directory/create_advance_payment_type.html',
                success_message='Вид авансового платежа '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_advance_payment_type'
    ),
    url(
        r'^advance-payment-types/(?P<pk>\d+)/edit/$',
        permissions('advance_payment_types_edit')(
            GenericUpdateView.as_view(
                model=models.AdvancePaymentTypes,
                form_class=forms.EditAdvancePaymentType,
                template_name='directory/edit_advance_payment_type.html',
                success_message='Вид авансового платежа '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_edit_advance_payment_type'
    ),
    url(
        r'^advance-payment-types-delete/(?P<pk>\d+)/$',
        permissions('advance_payment_types_delete')(
            GenericDeleteView.as_view(
                model=models.AdvancePaymentTypes,
            )
        ),
        name='directory_delete_advance_payment_type'
    ),

    # Car manufacturer
    url(
        r'^car-manufacturer/list/$',
        permissions('car_manufacturer_list')(
            GenericListView.as_view(
                model=models.CarManufacturer,
                template_name='directory/list_car_manufacturer.html'
            )
        ),
        name='directory_list_car_manufacturer'
    ),
    url(
        r'^car-manufacturer/create/$',
        permissions('car_manufacturer_create')(
            GenericCreateView.as_view(
                model=models.CarManufacturer,
                template_name='directory/create_car_manufacturer.html',
                form_class=forms.CreateCarManufacturerForm,
                success_message='Марка автомобиля '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлена'
            )
        ),
        name='directory_create_car_manufacturer'
    ),
    url(
        r'^car-manufacturer/(?P<pk>\d+)/detail/$',
        permissions('car_manufacturer_detail')(
            GenericDetailView.as_view(
                model=models.CarManufacturer,
                template_name='directory/detail_car_manufacturer.html',
            )
        ),
        name='directory_detail_car_manufacturer'
    ),
    url(
        r'^car-manufacturer/(?P<pk>\d+)/edit/$',
        permissions('car_manufacturer_edit')(
            GenericUpdateView.as_view(
                model=models.CarManufacturer,
                template_name='directory/edit_car_manufacturer.html',
                form_class=forms.CreateCarManufacturerForm,
                success_message='Марка автомобиля '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_edit_car_manufacturer'
    ),
    url(
        r'^car-manufacturer-delete/(?P<pk>\d+)/$',
        permissions('car_manufacturer_delete')(
            GenericDeleteView.as_view(
                model=models.CarManufacturer,
            )
        ),
        name='directory_delete_car_manufacturer'
    ),

    # Car model
    url(
        r'^car-model/list/$',
        permissions('car_model_list')(
            GenericListView.as_view(
                model=models.CarModel,
                template_name='directory/list_car_model.html',
                filter_form=filters.CarModelFilterForm,
                filter_type='car_model'
            )
        ),
        name='directory_list_car_model'
    ),
    url(
        r'^car-model/create/$',
        permissions('car_model_create')(
            GenericCreateView.as_view(
                model=models.CarModel,
                template_name='directory/create_car_model.html',
                form_class=forms.CreateCarModelForm,
                success_message='Модель автомобиля '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлена'
            )
        ),
        name='directory_create_car_model'
    ),
    url(
        r'^car-model/(?P<pk>\d+)/detail/$',
        permissions('car_model_detail')(
            GenericDetailView.as_view(
                model=models.CarModel,
                template_name='directory/detail_car_model.html',
            )
        ),
        name='directory_detail_car_model'
    ),
    url(
        r'^car-model/(?P<pk>\d+)/edit/$',
        permissions('car_model_edit')(
            GenericUpdateView.as_view(
                model=models.CarModel,
                template_name='directory/edit_car_model.html',
                form_class=forms.CreateCarModelForm,
                success_message='Модель автомобиля '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_edit_car_model'
    ),
    url(
        r'^car-model-delete/(?P<pk>\d+)/$',
        permissions('car_model_delete')(
            GenericDeleteView.as_view(
                model=models.CarModel,
            )
        ),
        name='directory_delete_car_model'
    ),

    # Car registration number type
    url(
        r'^car-reg-number-type/list/$',
        permissions('car_reg_number_type_list')(
            GenericListView.as_view(
                model=models.CarRegNumberType,
                template_name='directory/list_car_reg_number_type.html',
            )
        ),
        name='directory_list_car_reg_number_type'
    ),
    url(
        r'^car-reg-number-type/create/$',
        permissions('car_reg_number_type_create')(
            GenericCreateView.as_view(
                model=models.CarRegNumberType,
                template_name='directory/create_car_reg_number_type.html',
                form_class=forms.CreateCarRegNumberTypeForm,
                success_message='Тип номера государственной регистрации '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_car_reg_number_type'
    ),
    url(
        r'^car-reg-number-type/(?P<pk>\d+)/detail/$',
        permissions('car_reg_number_type_detail')(
            GenericDetailView.as_view(
                model=models.CarRegNumberType,
                template_name='directory/detail_car_reg_number_type.html',
            )
        ),
        name='directory_detail_car_reg_number_type'
    ),
    url(
        r'^car-reg-number-type/(?P<pk>\d+)/edit/$',
        permissions('car_reg_number_type_edit')(
            GenericUpdateView.as_view(
                model=models.CarRegNumberType,
                template_name='directory/edit_car_reg_number_type.html',
                form_class=forms.CreateCarRegNumberTypeForm,
                success_message='Тип дополнительного номера '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_edit_car_reg_number_type'
    ),
    url(
        r'^car-reg-number-type/(?P<pk>\d+)/$',
        permissions('car_reg_number_type_delete')(
            GenericDeleteView.as_view(
                model=models.CarRegNumberType,
            )
        ),
        name='directory_delete_car_reg_number_type'
    ),

    # Car additional registration number type
    url(
        r'^car-ad-reg-number-type/list/$',
        permissions('car_ad_reg_number_type_list')(
            GenericListView.as_view(
                model=models.CarAdRegNumberType,
                template_name='directory/list_car_ad_reg_number_type.html',
            )
        ),
        name='directory_list_car_ad_reg_number_type'
    ),
    url(
        r'^car-ad-reg-number-type/create/$',
        permissions('car_ad_reg_number_type_create')(
            GenericCreateView.as_view(
                model=models.CarAdRegNumberType,
                template_name='directory/create_car_ad_reg_number_type.html',
                form_class=forms.CreateCarAdRegNumberTypeForm,
                success_message='Тип дополнительного номера '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_car_ad_reg_number_type'
    ),
    url(
        r'^car-ad-reg-number-type/(?P<pk>\d+)/detail/$',
        permissions('car_ad_reg_number_type_detail')(
            GenericDetailView.as_view(
                model=models.CarAdRegNumberType,
                template_name='directory/detail_car_ad_reg_number_type.html',
            )
        ),
        name='directory_detail_car_ad_reg_number_type'
    ),
    url(
        r'^car-ad-reg-number-type/(?P<pk>\d+)/edit/$',
        permissions('car_ad_reg_number_type_edit')(
            GenericUpdateView.as_view(
                model=models.CarAdRegNumberType,
                template_name='directory/edit_car_ad_reg_number_type.html',
                form_class=forms.CreateCarAdRegNumberTypeForm,
                success_message='Тип дополнительного номера '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_edit_car_ad_reg_number_type'
    ),
    url(
        r'^car-ad-reg-number-type/(?P<pk>\d+)/$',
        permissions('car_ad_reg_number_type_delete')(
            GenericDeleteView.as_view(
                model=models.CarAdRegNumberType,
            )
        ),
        name='directory_delete_car_ad_reg_number_type'
    ),

    # Car type
    url(
        r'^car-type/list/$',
        permissions('car_type_list')(
            GenericListView.as_view(
                model=models.CarType,
                template_name='directory/list_car_type.html',
            )
        ),
        name='directory_list_car_type'
    ),
    url(
        r'^car-type/create/$',
        permissions('car_type_create')(
            GenericCreateView.as_view(
                model=models.CarType,
                template_name='directory/create_car_type.html',
                form_class=forms.CreateCarTypeForm,
                success_message='Тип автомобиля '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлен'
            )
        ),
        name='directory_create_car_type'
    ),
    url(
        r'^car-type/(?P<pk>\d+)/detail/$',
        permissions('car_type_detail')(
            GenericDetailView.as_view(
                model=models.CarType,
                template_name='directory/detail_car_type.html',
            )
        ),
        name='directory_detail_car_type'
    ),
    url(
        r'^car-type/(?P<pk>\d+)/edit/$',
        permissions('car_type_edit')(
            GenericUpdateView.as_view(
                model=models.CarType,
                template_name='directory/edit_car_type.html',
                form_class=forms.CreateCarTypeForm,
                success_message='Тип автомобиля '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменен'
            )
        ),
        name='directory_edit_car_type'
    ),
    url(
        r'^car-type/(?P<pk>\d+)/$',
        permissions('car_type_delete')(
            GenericDeleteView.as_view(
                model=models.CarType,
            )
        ),
        name='directory_delete_car_type'
    ),

    # Format logic control error
    url(
        r'^format-logic-control-error/list/$',
        permissions('format_logic_control_error_list')(
            GenericListView.as_view(
                model=models.FormatLogicControlError,
                queryset=models.FormatLogicControlError.objects.order_by('id'),
                template_name='directory/list_format_logic_control_error.html',
                filter_form=filters.FormatLogicControlErrorFilterForm,
                filter_type='format_logic_control_error'
            )
        ),
        name='directory_format_logic_control_error_list'
    ),
    url(
        r'^format-logic-control-error/create/$',
        permissions('format_logic_control_error_create')(
            GenericCreateView.as_view(
                model=models.FormatLogicControlError,
                template_name='directory/'
                              'create_format_logic_control_error.html',
                form_class=forms.CreateFormatLogicControlErrorForm,
                success_message='Ошибка форматно-логического контроля '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлена'
            )
        ),
        name='directory_format_logic_control_error_create'
    ),
    url(
        r'^format-logic-control-error/(?P<pk>\d+)/detail/$',
        permissions('format_logic_control_error_detail')(
            GenericDetailView.as_view(
                model=models.FormatLogicControlError,
                template_name='directory/detail_format_logic_control'
                              '_error.html',
            )
        ),
        name='directory_format_logic_control_error_detail'
    ),
    url(
        r'^format-logic-control-error/(?P<pk>\d+)/edit/$',
        permissions('format_logic_control_error_edit')(
            GenericUpdateView.as_view(
                model=models.FormatLogicControlError,
                template_name='directory/edit_format_logic_control_error.html',
                form_class=forms.EditFormatLogicControlErrorForm,
                success_message='Ошибка форматно-логического контроля '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменена'
            )
        ),
        name='directory_format_logic_control_error_edit'
    ),
    url(
        r'^cashbox-format-logic-control-error/(?P<pk>\d+)/delete/$',
        permissions('format_logic_control_error_delete')(
            GenericDeleteView.as_view(
                model=models.FormatLogicControlError,
            )
        ),
        name='directory_format_logic_control_error_delete'
    ),

    # Reason blocking work
    url(
        r'^reason-blocking-work/list/$',
        permissions('reason_blocking_work_list')(
            GenericListView.as_view(
                model=models.ReasonBlockingWork,
                template_name='directory/list_reason_blocking_work.html',
                filter_form=filters.ReasonBlockingWorkFilterForm,
                filter_type='reason_blocking_work'
            )
        ),
        name='directory_reason_blocking_work_list'
    ),
    url(
        r'^reason-blocking-work/create/$',
        permissions('reason_blocking_work_create')(
            GenericCreateView.as_view(
                model=models.ReasonBlockingWork,
                template_name='directory/create_reason_blocking_work.html',
                form_class=forms.CreateReasonBlockingWorkForm,
                success_message='Основания блокирования работы КО '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно добавлено'
            )
        ),
        name='directory_reason_blocking_work_create'
    ),
    url(
        r'^reason-blocking-work/(?P<pk>\d+)/detail/$',
        permissions('reason_blocking_work_detail')(
            GenericDetailView.as_view(
                model=models.ReasonBlockingWork,
                template_name='directory/detail_reason_blocking_work.html',
            )
        ),
        name='directory_reason_blocking_work_detail'
    ),
    url(
        r'^reason-blocking-work/(?P<pk>\d+)/edit/$',
        permissions('reason_blocking_work_edit')(
            GenericUpdateView.as_view(
                model=models.ReasonBlockingWork,
                template_name='directory/edit_reason_blocking_work.html',
                form_class=forms.EditReasonBlockingWorkForm,
                success_message='Основания блокирования работы КО '
                                '<a href="{{ object.get_absolute_url }}">'
                                '{{ object }}</a> успешно изменено'
            )
        ),
        name='directory_reason_blocking_work_edit'
    ),
    url(
        r'^reason-blocking-work/(?P<pk>\d+)/delete/$',
        permissions('reason_blocking_work_delete')(
            GenericDeleteView.as_view(
                model=models.ReasonBlockingWork,
            )
        ),
        name='directory_reason_blocking_work_delete'
    ),
    url(
        r'^unloading-list-format-logic-control-error/$',
        views.unloading_list_format_logic_control_error,
        name='unloading_list_format_logic_control_error'
    ),
    #  Trading object purpose groups
    url(
        r'^trading-object-purpose-group/list/$',
        permissions('trading_object_purpose_group_list')(
            GenericListView.as_view(
                model=models.TradingObjectPurposeTypesGroups,
                template_name='directory/list_trading_object_purpose_groups.html',
                filter_form=filters.TradingObjectPurposeGroupsFilterForm,
                filter_type='trading_object_purpose_group')
        ),
        name='directory_trading_object_purpose_group_list'
    ),
    url(
        r'^trading-object-purpose-group/create/$',
        views.create_trading_object_purpose_group,
        name='directory_trading_object_purpose_group_create'
    ),
    url(
        r'^trading-object-purpose-group/(?P<pk>\d+)/detail/$',
        permissions('trading_object_purpose_group_detail')(
            GenericDetailView.as_view(
                model=models.TradingObjectPurposeTypesGroups,
                template_name='directory/detail_trading_object_purpose_group.html')
        ),
        name='directory_trading_object_purpose_group_detail'
    ),
    url(
        r'^trading-object-purpose-group/(?P<pk>\d+)/edit/$',
        views.edit_trading_object_purpose_group,
        name='directory_trading_object_purpose_group_edit'
    ),
    url(
        r'^user_notifications/$',
        views.user_notification_list,
        name='directory_user_notification'
    ),
    url(
        r'^user_notifications/create/$',
        views.user_notification_create,
        name='user_notification_create'
    ),
    url(
        r'^user_notifications/(?P<pk>\d+)/edit/$',
        views.user_notification_edit,
        name='user_notification_edit'
    ),
    url(
        r'^user_notifications/(?P<pk>\d+)/detail/$',
        views.user_notification_detail, 
        name='user_notification_detail'
    ),
    url(
        r'^user_notifications/(?P<pk>\d+)/delete/$',
        permissions('directory_user_notification_delete')(
            GenericDeleteView.as_view(
                model=models.UserNotification,
            )
        ),
        name='user_notification_delete'
    ),
)
