# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('directory', '0092_auto_20240610_1148'),
    ]

    operations = [
        migrations.AlterField(
            model_name='tradingobjectpurposetypesgroups',
            name='trading_object_groups',
            field=models.ManyToManyField(related_name='trading_object_purpose_groups', verbose_name='\u0413\u0440\u0443\u043f\u043f\u0430 \u0442\u0438\u043f\u043e\u0432 \u0442\u043e\u0440\u0433\u043e\u0432\u043e\u0433\u043e \u043e\u0431\u044a\u0435\u043a\u0442\u0430', to='directory.TradingObjectTypesGroups'),
            preserve_default=True,
        ),
    ]
