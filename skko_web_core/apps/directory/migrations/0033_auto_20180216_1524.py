# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations
import datetime


class Migration(migrations.Migration):

    dependencies = [
        ('directory', '0032_auto_20180215_1525'),
    ]

    operations = [
        migrations.AlterField(
            model_name='externaldirectoryload',
            name='details',
            field=models.TextField(default='', verbose_name='\u041f\u043e\u0434\u0440\u043e\u0431\u043d\u043e\u0441\u0442\u0438 \u0432\u044b\u043f\u043e\u043b\u043d\u0435\u043d\u0438\u044f', blank=True),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='externaldirectoryload',
            name='rows_count',
            field=models.IntegerField(default=0, verbose_name='\u041a\u043e\u043b-\u0432\u043e \u0437\u0430\u0433\u0440\u0443\u0436\u0435\u043d\u043d\u044b\u0445 \u0441\u0442\u0440\u043e\u043a'),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='externaldirectoryload',
            name='start_time',
            field=models.DateTimeField(default=datetime.datetime.now, verbose_name='\u0414\u0430\u0442\u0430 \u0438 \u0432\u0440\u0435\u043c\u044f \u043d\u0430\u0447\u0430\u043b\u0430 \u0437\u0430\u0433\u0440\u0443\u0437\u043a\u0438'),
            preserve_default=True,
        ),
        migrations.RenameField(
            model_name='soato',
            old_name='city_code',
            new_name='cadastral_id',
        ),
    ]
