# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('directory', '0028_auto_20170614_1503'),
    ]

    operations = [
        migrations.AddField(
            model_name='mns',
            name='liquidation_date',
            field=models.DateField(null=True, verbose_name='\u0414\u0430\u0442\u0430 \u043b\u0438\u043a\u0432\u0438\u0434\u0430\u0446\u0438\u0438', db_column='date_to', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='mns',
            name='main_imns',
            field=models.CharField(default='0', max_length=3, verbose_name='\u0418\u041c\u041d\u0421', db_column='num2', blank=True),
            preserve_default=False,
        ),
        migrations.Add<PERSON>ield(
            model_name='mns',
            name='main_unp',
            field=models.CharField(default='', max_length=9, verbose_name='\u0423\u041d\u041f \u0418\u041c\u041d\u0421 \u0434\u043b\u044f \u043a\u0430\u0437\u043d\u0430\u0447\u0435\u0439\u0441\u0442\u0432\u043e', db_column='unp2', blank=True),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='mns',
            name='full_name',
            field=models.CharField(max_length=255, verbose_name='\u041f\u043e\u043b\u043d\u043e\u0435 \u043d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435', db_column='dname', blank=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='mns',
            name='name',
            field=models.CharField(max_length=255, verbose_name='\u041d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435', blank=True),
            preserve_default=True,
        ),
    ]
