# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('directory', '0107_usernotification'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='usernotification',
            options={'verbose_name': '\u0421\u043e\u043e\u0431\u0449\u0435\u043d\u0438\u044f \u0434\u043b\u044f \u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u0435\u0439'},
        ),
        migrations.AddField(
            model_name='usernotification',
            name='file_name',
            field=models.CharField(max_length=500, null=True, verbose_name='\u041d\u0430\u0437\u0432\u0430\u043d\u0438\u0435 \u0444\u0430\u0439\u043b\u0430', blank=True),
            preserve_default=True,
        ),
    ]
