# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0193_auto_20220323_1150'),
        ('directory', '0069_auto_20220316_1117'),
    ]

    operations = [
        migrations.AddField(
            model_name='discount',
            name='filter_applicant_of_cashbox',
            field=models.ManyToManyField(to='directory.ApplicantOfCashBox', null=True, verbose_name='\u0417\u0430\u044f\u0432\u0438\u0442\u0435\u043b\u044c \u043a\u0430\u0441\u0441\u043e\u0432\u043e\u0433\u043e \u043e\u0431\u043e\u0440\u0443\u0434\u043e\u0432\u0430\u043d\u0438\u044f', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='discount',
            name='filter_business_entity',
            field=models.ManyToManyField(to='core.BusinessEntity', null=True, verbose_name='\u0421\u041f\u0414', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='discount',
            name='filter_connected_time_end',
            field=models.DateField(null=True, verbose_name='\u0414\u0430\u0442\u0430 \u043a\u043e\u043d\u0446\u0430 \u043f\u0435\u0440\u0438\u043e\u0434\u0430 \u043f\u043e\u0434\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u044f \u043a \u0410\u0418\u0421 \u041a\u041a\u041e', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='discount',
            name='filter_connected_time_start',
            field=models.DateField(null=True, verbose_name='\u0414\u0430\u0442\u0430 \u043d\u0430\u0447\u0430\u043b\u0430 \u043f\u0435\u0440\u0438\u043e\u0434\u0430 \u043f\u043e\u0434\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u044f \u043a \u0410\u0418\u0421 \u041a\u041a\u041e', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='discount',
            name='filter_pcs_operator',
            field=models.ManyToManyField(to='core.PCSOperator', null=True, verbose_name='\u043e\u043f\u0435\u0440\u0430\u0442\u043e\u0440 \u041f\u041a\u0421', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='discount',
            name='filter_user_type',
            field=models.IntegerField(blank=True, null=True, verbose_name='\u0422\u0438\u043f \u0432\u043b\u0430\u0434\u0435\u043b\u044c\u0446\u0430 \u0421\u041f\u0414', choices=[(1, b'\xd0\x98\xd0\xbd\xd0\xb4\xd0\xb8\xd0\xb2\xd0\xb8\xd0\xb4\xd1\x83\xd0\xb0\xd0\xbb\xd1\x8c\xd0\xbd\xd1\x8b\xd0\xb9 \xd0\xbf\xd1\x80\xd0\xb5\xd0\xb4\xd0\xbf\xd1\x80\xd0\xb8\xd0\xbd\xd0\xb8\xd0\xbc\xd0\xb0\xd1\x82\xd0\xb5\xd0\xbb\xd1\x8c'), (2, b'\xd0\xae\xd1\x80\xd0\xb8\xd0\xb4\xd0\xb8\xd1\x87\xd0\xb5\xd1\x81\xd0\xba\xd0\xbe\xd0\xb5 \xd0\xbb\xd0\xb8\xd1\x86\xd0\xbe')]),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='discount',
            name='discount_type',
            field=models.IntegerField(verbose_name='\u0422\u0438\u043f \u0443\u0441\u043b\u0443\u0433\u0438', choices=[(1, '\u0420\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u044f \u0438 \u043f\u043e\u0434\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u0435 \u0432 \u0421\u041a\u041a\u041e'), (2, '\u0410\u0431\u043e\u043d\u0435\u043d\u0442\u0441\u043a\u0430\u044f \u043f\u043b\u0430\u0442\u0430 (\u0437\u0430 \u043c\u0435\u0441\u044f\u0446)'), (3, '\u041c\u0435\u0441\u044f\u0447\u043d\u044b\u0439 \u0442\u0430\u0440\u0438\u0444 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u043e\u043d\u043d\u043e\u0433\u043e \u043e\u0431\u0441\u043b\u0443\u0436\u0438\u0432\u0430\u043d\u0438\u044f \u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u044f \u0421\u041a\u041a\u041e'), (4, '\u0410\u0431\u043e\u043d\u0435\u043d\u0442\u0441\u043a\u0430\u044f \u043f\u043b\u0430\u0442\u0430 (\u0437\u0430 \u0441\u0443\u0442\u043a\u0438)'), (5, '\u0421\u0443\u0442\u043e\u0447\u043d\u044b\u0439 \u0442\u0430\u0440\u0438\u0444 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u043e\u043d\u043d\u043e\u0433\u043e \u043e\u0431\u0441\u043b\u0443\u0436\u0438\u0432\u0430\u043d\u0438\u044f \u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u044f \u0421\u041a\u041a\u041e')]),
            preserve_default=True,
        ),
    ]
