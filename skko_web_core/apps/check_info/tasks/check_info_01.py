# encoding: utf-8
from __future__ import unicode_literals

import datetime
import logging
import os
from functools import partial

from django.db.models import Q
from ezodf import newdoc

from skko_web_core.celery_app import celery_app
from skko_web_core.apps.check_info.models import UsersRequests
from skko_web_core.apps.constants.check_info import CHECK_INFO_REPORTS
from skko_web_core.apps.constants.core import CASHBOX_TYPES
from skko_web_core.apps.core import models as core_models
from skko_web_core.apps.core.simpleprofiler import SimpleProfiler
from skko_web_core.apps.reports.utils import set_task_status_and_upload_xlsx, copy_row
from skko_web_core.settings import (
    CHECK_INFO_REPORT_DIR, DATETIME_INPUT_FORMATS, ENABLE_REPORT_PROFILER,
    QUEUE_MANUAL, SSL_ID, TMP_DIR, UL_ID,
)

logger = logging.getLogger(__name__)

ReportProfiler = partial(SimpleProfiler, enabled=ENABLE_REPORT_PROFILER)


@celery_app.task(queue=QUEUE_MANUAL)
@set_task_status_and_upload_xlsx
def check_info_01(task, **kwargs):
    start_date = kwargs.get('start_date')
    end_date = kwargs.get('end_date')
    time_of_creation = task.started_at.strftime(DATETIME_INPUT_FORMATS[0])
    assert start_date is not None, "don't start date in kwargs"
    assert end_date is not None, "don't end date in kwargs"
    period_start_datetime = datetime.datetime.combine(start_date,
                                                      datetime.time.min)
    period_end_datetime = datetime.datetime.combine(end_date,
                                                    datetime.time.max)

    file_path = os.path.join(TMP_DIR, 'tmp_check_info_01_report%s.ods' % task.pk)
    # file_path = os.path.join(TMP_DIR, 'tmp_check_info_report%s.ods' % task)
    template = os.path.join(CHECK_INFO_REPORT_DIR, 'check_info_01.ods')
    report = newdoc(filename=file_path, template=template)
    sheet = report.sheets[0]

    title = CHECK_INFO_REPORTS.get('check_info_01')
    # title = '1. Отчет по проверкам чеков покупателями за период c {0} по {1} ' \
    #         'в разрезе КО, ТО'.format(start_date, end_date)
    sheet['A2'].set_value(title)
    sheet['C3'].set_value('c {0} по {1}'.format(start_date, end_date))
    sheet['A9'].set_value(time_of_creation)
    sheet['C3'].set_value('c {0} по {1}'.format(start_date.strftime('%d.%m.%Y'),
                                                end_date.strftime('%d.%m.%Y')))
    # Номер строки, с которой начнут записываться данные в таблицу
    start_row = 11
    consec_number = 1

    # TODO: ADD user permissions by imns
    if task.user.role.method == UL_ID:
        q_filter = Q(trading_object__pk__in=task.user.get_to_pks())
    elif task.user.role.method == SSL_ID:
        q_filter = Q(trading_object__imns__in=task.user.imns)
        if task.user.has_perm('view_data_for_all_controller'):
            q_filter |= Q(
                trading_object__object_type__access_to_all_controllers=True)
    else:
        q_filter = Q()

    if kwargs.get("trading_objects_types"):
        trading_objects_types_filter = [name for id_, name in kwargs.get("trading_objects_types")]
        sheet['C8'].set_value(", ".join(trading_objects_types_filter))
        filter_trading_objects_types = Q(
            trading_object__object_type__in=[id_ for id_, name in kwargs.get("trading_objects_types")]
        )
    else:
        filter_trading_objects_types = Q()
    if kwargs.get("cashbox"):
        sheet['C6'].set_value(", ".join(kwargs.get("cashbox")))
        filter_cashbox = Q(account_number__in=kwargs.get("cashbox"))
    else:
        filter_cashbox = Q()

    if kwargs.get("cashbox_type"):
        sheet['C7'].set_value(CASHBOX_TYPES[int(kwargs.get("cashbox_type"))][1])
        filter_cashbox_type = Q(cashbox_type__in=kwargs.get("cashbox_type"))
    else:
        filter_cashbox_type = Q()

    users_requests_period = UsersRequests.objects \
        .using('check_info') \
        .filter(created_at__gt=period_start_datetime,
                created_at__lt=period_end_datetime,
                status=1)

    cashboxes_users_requests = users_requests_period \
        .values_list('verified_checks__check_archive__cashbox_number', flat=True)

    cashboxes = core_models.CashBox.objects \
        .filter(
            q_filter,
            filter_trading_objects_types,
            filter_cashbox,
            filter_cashbox_type,
            account_number__in=set(cashboxes_users_requests)
        )
    if kwargs.get("unp"):
        unp = kwargs.get("unp")
        sheet['C5'].set_value(unp)
        organizations = core_models.BusinessEntity.objects.filter(unp=unp)
    else:
        organizations = cashboxes.values_list("trading_object__organization",
                                              flat=True)
    if kwargs.get("imns"):
        sheet['C4'].set_value(kwargs.get("imns"))
        organizations_imns = [kwargs.get("imns"), ]
    else:
        organizations_imns = cashboxes.values_list(
            "trading_object__organization__imns", flat=True)

    be_all = core_models.BusinessEntity.objects.filter(
        imns__in=set(organizations_imns)
    )
    for org_imns in sorted(set(organizations_imns)):
        imns_count = 0

        for organization in be_all.filter(imns=org_imns, id__in=organizations):
            org_count = 0
            # Заполнение таблицы данными
            for cash_box in cashboxes.filter(trading_object__organization=organization):
                cb_number = cash_box.account_number
                users_requests = users_requests_period.filter(
                    verified_checks__check_archive__cashbox_number=cb_number)
                users_requests_count = users_requests.count()
    # --------------- Наполнение данными ---------------------------------------
                if users_requests_count > 0:
                    copy_row(sheet, start_row, start_row + 1)

                    sheet[start_row, 0].set_value(consec_number)
                    sheet[start_row, 1].set_value(organization.imns)
                    sheet[start_row, 2].set_value(cash_box.trading_object.imns)
                    sheet[start_row, 3].set_value(organization.unp)
                    sheet[start_row, 4].set_value(organization.full_name)
                    sheet[start_row, 5].set_value(cash_box.trading_object.name)
                    sheet[start_row, 6].set_value(str(cash_box.account_number))
                    sheet[start_row, 7].set_value(cash_box.get_cashbox_type_display())
                    sheet[start_row, 8].set_value(cash_box.full_address)
                    sheet[start_row, 9].set_value(users_requests_count)

                    consec_number += 1
                    start_row += 1

                    org_count += users_requests_count

            # Итого за по УНП
            copy_row(sheet, start_row, start_row + 1)
            sheet[start_row, 8].set_value('Итого по УНП:')
            sheet[start_row, 9].set_value(org_count)
            start_row += 1

            imns_count += org_count

        # Итого за по ИМНС
        if imns_count > 0:
            copy_row(sheet, start_row, start_row + 1)
            sheet[start_row, 8].set_value('Итого по ИМНС {}:'.format(org_imns))
            sheet[start_row, 9].set_value(imns_count)
            start_row += 1

# --Для полного фен шуя удаляется последняя строка таблицы, т.к. она без данных-
    sheet.delete_rows(sheet.nrows() - 1)
# -----------------------------------------------------------------------------
    report.save()
    return report.docname
