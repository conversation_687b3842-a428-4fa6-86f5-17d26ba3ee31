# coding: utf-8
from __future__ import unicode_literals

from django.db import models


class AbstractCreateUpdate(models.Model):
    created_at = models.DateTimeField(
        verbose_name='Дата создания',
        auto_now_add=True
    )
    updated_at = models.DateTimeField(
        verbose_name='Дата редактирования',
        auto_now=True
    )

    class Meta:
        abstract = True


class AbstractCheck(models.Model):
    """
    Первичные данные чека
    """
    id = models.BigIntegerField()
    ui = models.DecimalField(primary_key=True,
                             decimal_places=0,
                             max_digits=30,
                             verbose_name='Уникальный идентификатор')
    cashbox_number = models.IntegerField(
        verbose_name='Регистрационный номер КО в АИС ККО',)
    doc_number = models.IntegerField(
        verbose_name='Номер платежного документа'
    )
    issued_at = models.DateTimeField(
        verbose_name='Дата, время выдачи платежного документа'
    )
    payment_amount = models.DecimalField(decimal_places=2,
                                         max_digits=100,
                                         verbose_name='Итого к оплате')
    factory_number = models.CharField(max_length=20,
                                      verbose_name='Серийный номер СКНО')

    class Meta:
        abstract = True
