# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('device', '0031_auto_20241226_1646'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cashboxfirmwarehistory',
            name='status',
            field=models.IntegerField(default=10, db_index=True, verbose_name='\u0421\u0442\u0430\u0442\u0443\u0441 \u043f\u0440\u043e\u0448\u0438\u0432\u043a\u0438', choices=[(10, '\u0412 \u043e\u0447\u0435\u0440\u0435\u0434\u0438 \u043d\u0430 \u043f\u0440\u043e\u0448\u0438\u0432\u043a\u0443'), (20, '\u041f\u0435\u0440\u0435\u043d\u0430\u043f\u0440\u0430\u0432\u043b\u0435\u043d \u043d\u0430 \u0441\u0435\u0440\u0432\u0435\u0440 \u043f\u0440\u043e\u0448\u0438\u0432\u043a\u0438'), (30, '\u0418\u0434\u0435\u0442 \u043f\u0440\u043e\u0448\u0438\u0432\u043a\u0430'), (40, '\u041e\u0436\u0438\u0434\u0430\u043d\u0438\u0435 \u043f\u0435\u0440\u0435\u0437\u0430\u0433\u0440\u0443\u0437\u043a\u0438'), (50, '\u0421\u041a\u041d\u041e \u043f\u0435\u0440\u0435\u0437\u0430\u0433\u0440\u0443\u0436\u0435\u043d\u043e'), (60, '\u041f\u0440\u043e\u0448\u0438\u0432\u043a\u0430 \u043f\u0440\u0438\u043c\u0435\u043d\u0435\u043d\u0430 \u0443\u0441\u043f\u0435\u0448\u043d\u043e'), (100, '\u041e\u0448\u0438\u0431\u043a\u0430 \u043f\u0440\u043e\u0448\u0438\u0432\u043a\u0438')]),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='cashboxfirmwarehistory',
            name='status_40_at',
            field=models.DateTimeField(null=True, verbose_name='\u041e\u0436\u0438\u0434\u0430\u043d\u0438\u0435 \u043f\u0435\u0440\u0435\u0437\u0430\u0433\u0440\u0443\u0437\u043a\u0438', blank=True),
            preserve_default=True,
        ),
    ]
