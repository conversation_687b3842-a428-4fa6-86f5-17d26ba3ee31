# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('audit', '0008_auto_20170517_1502'),
    ]

    operations = [
        migrations.AlterField(
            model_name='audit',
            name='event',
            field=models.PositiveSmallIntegerField(verbose_name='\u0421\u043e\u0431\u044b\u0442\u0438\u0435', choices=[(1, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430'), (2, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440'), (3, '\u0441\u043e\u0437\u0434\u0430\u043d\u0438\u0435'), (4, '\u0440\u0435\u0434\u0430\u043a\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435'), (5, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u0438'), (6, '\u0443\u0434\u0430\u043b\u0435\u043d\u0438\u0435'), (7, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u044b\u0445 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u043e\u0432'), (8, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 Z-\u043e\u0442\u0447\u0435\u0442\u043e\u0432'), (9, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0432\u043d\u0435\u0441\u0435\u043d\u0438\u0439/\u0438\u0437\u044a\u044f\u0442\u0438\u0439'), (10, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0432\u043e\u0437\u0432\u0440\u0430\u0442\u043e\u0432'), (11, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0430\u043d\u043d\u0443\u043b\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0439'), (12, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430 \u043a\u0430\u0441\u0441\u043e\u0432\u043e\u0433\u043e \u043e\u0431\u043e\u0440\u0443\u0434\u043e\u0432\u0430\u043d\u0438\u044f'), (13, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430 \u0442\u043e\u0440\u0433\u043e\u0432\u044b\u0445 \u043e\u0431\u044a\u0435\u043a\u0442\u043e\u0432'), (14, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u043e\u0432 \u0441 \u0426\u0422\u041e'), (15, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u043e\u0432 \u0441 \u0410\u0418\u0421 \u0421\u041a\u041a\u041e'), (16, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430 \u043f\u043e\u0434\u0440\u0430\u0437\u0434\u0435\u043b\u0435\u043d\u0438\u0439'), (17, '\u043f\u0440\u043e\u0441\u043c\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430 \u043a\u043e\u043c\u043f\u043b\u0435\u043a\u0442\u0443\u044e\u0449\u0438\u0445'), (18, '\u0434\u043e\u0431\u0430\u0432\u043b\u0435\u043d\u0438\u0435 \u043a\u043e\u043c\u043f\u043b\u0435\u043a\u0442\u0443\u044e\u0449\u0438\u0445 \u0432 \u043f\u0430\u0440\u0442\u0438\u044e'), (19, '\u0432\u0445\u043e\u0434 \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0443'), (20, '\u0432\u044b\u0445\u043e\u0434 \u0438\u0437 \u0441\u0438\u0441\u0442\u0435\u043c\u044b'), (21, '\u043f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043e\u0431\u044b\u0442\u0438\u0439 \u043a\u0430\u0441\u0441\u044b'), (22, '\u0441\u043d\u0430\u0440\u044f\u0436\u0435\u043d\u0438\u0435 \u0421\u041a\u041d\u041e'), (23, '\u0443\u0441\u0442\u0430\u043d\u043e\u0432\u043a\u0430 \u0421\u041a\u041d\u041e'), (24, '\u0440\u0430\u0437\u0443\u043a\u043e\u043c\u043f\u043b\u0435\u043a\u0442\u0430\u0446\u0438\u044f \u0421\u041a\u041d\u041e'), (25, '\u0432\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u0435 \u0421\u041a\u041d\u041e'), (26, '\u0432\u044b\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u0435 \u0421\u041a\u041d\u041e'), (27, '\u0441\u043d\u044f\u0442\u0438\u0435 \u0421\u041a\u041d\u041e'), (28, '\u0444\u043e\u0440\u043c\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u0441\u0435\u0440\u0432\u0438\u0441\u0430'), (29, '\u0444\u043e\u0440\u043c\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u043e\u0442\u0447\u0435\u0442\u0430'), (30, '\u0432\u044b\u0433\u0440\u0443\u0437\u043a\u0430 \u0434\u0430\u043d\u043d\u044b\u0445'), (31, '\u0432\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0435 \u0438\u0437 \u0430\u0440\u0445\u0438\u0432\u0430')]),
            preserve_default=True,
        ),
    ]
