# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations
import postgres.fields


class Migration(migrations.Migration):

    dependencies = [
        ('audit', '0020_auto_20231212_1029'),
    ]

    operations = [
        migrations.AlterField(
            model_name='audit',
            name='event',
            field=models.PositiveSmallIntegerField(verbose_name='\u0421\u043e\u0431\u044b\u0442\u0438\u0435', choices=[(1, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430'), (3, '\u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435'), (2, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440'), (4, '\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435'), (5, '\u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435'), (6, '\u0412\u044b\u0433\u0440\u0443\u0437\u043a\u0430'), (7, '\u0417\u0430\u0433\u0440\u0443\u0437\u043a\u0430'), (8, '\u0414\u0440\u0443\u0433\u043e\u0435'), (9, '\u0412\u0445\u043e\u0434 \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0443'), (10, '\u0412\u044b\u0445\u043e\u0434 \u0438\u0437 \u0441\u0438\u0441\u0442\u0435\u043c\u044b')]),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='limitedmonitoring',
            name='controlled_action',
            field=postgres.fields.ArrayField(models.SmallIntegerField(choices=[(1, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430'), (3, '\u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435'), (2, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440'), (4, '\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435'), (5, '\u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435'), (6, '\u0412\u044b\u0433\u0440\u0443\u0437\u043a\u0430'), (7, '\u0417\u0430\u0433\u0440\u0443\u0437\u043a\u0430'), (8, '\u0414\u0440\u0443\u0433\u043e\u0435'), (9, '\u0412\u0445\u043e\u0434 \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0443'), (10, '\u0412\u044b\u0445\u043e\u0434 \u0438\u0437 \u0441\u0438\u0441\u0442\u0435\u043c\u044b')]), verbose_name='\u0422\u0438\u043f \u043a\u043e\u043d\u0442\u0440\u043e\u043b\u0438\u0440\u0443\u0435\u043c\u044b\u0445 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0439 \u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u044f', size=None),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='limitedmonitoringevent',
            name='controlled_action',
            field=models.SmallIntegerField(verbose_name='\u0422\u0438\u043f \u043a\u043e\u043d\u0442\u0440\u043e\u043b\u0438\u0440\u0443\u0435\u043c\u044b\u0445 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0439 \u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u044f', choices=[(1, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430'), (3, '\u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435'), (2, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440'), (4, '\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435'), (5, '\u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435'), (6, '\u0412\u044b\u0433\u0440\u0443\u0437\u043a\u0430'), (7, '\u0417\u0430\u0433\u0440\u0443\u0437\u043a\u0430'), (8, '\u0414\u0440\u0443\u0433\u043e\u0435'), (9, '\u0412\u0445\u043e\u0434 \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0443'), (10, '\u0412\u044b\u0445\u043e\u0434 \u0438\u0437 \u0441\u0438\u0441\u0442\u0435\u043c\u044b')]),
            preserve_default=True,
        ),
    ]
