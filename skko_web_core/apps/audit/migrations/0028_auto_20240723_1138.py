# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations
import postgres.fields


class Migration(migrations.Migration):

    dependencies = [
        ('audit', '0027_auto_20240614_1715'),
    ]

    operations = [
        migrations.AlterField(
            model_name='audit',
            name='event',
            field=models.PositiveSmallIntegerField(verbose_name='\u0421\u043e\u0431\u044b\u0442\u0438\u0435', choices=[(1, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430'), (3, '\u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435'), (2, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440'), (4, '\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435'), (5, '\u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435'), (6, '\u0412\u044b\u0433\u0440\u0443\u0437\u043a\u0430'), (7, '\u0417\u0430\u0433\u0440\u0443\u0437\u043a\u0430'), (8, '\u0414\u0440\u0443\u0433\u043e\u0435'), (9, '\u0412\u0445\u043e\u0434 \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0443'), (10, '\u0412\u044b\u0445\u043e\u0434 \u0438\u0437 \u0441\u0438\u0441\u0442\u0435\u043c\u044b'), (11, '\u0420\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u044f \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0435'), (12, '\u0414\u043e\u0431\u0430\u0432\u043b\u0435\u043d\u0438\u0435 \u043a\u043e\u043c\u043f\u043b\u0435\u043a\u0442\u0443\u044e\u0449\u0438\u0445 \u0432 \u043f\u0430\u0440\u0442\u0438\u044e'), (13, '\u0421\u043d\u0430\u0440\u044f\u0436\u0435\u043d\u0438\u0435 \u0421\u041a\u041d\u041e'), (14, '\u0423\u0441\u0442\u0430\u043d\u043e\u0432\u043a\u0430 \u0421\u041a\u041d\u041e'), (15, '\u0420\u0430\u0437\u0443\u043a\u043e\u043c\u043f\u043b\u0435\u043a\u0442\u0430\u0446\u0438\u044f \u0421\u041a\u041d\u041e'), (16, '\u0421\u043d\u044f\u0442\u0438\u0435 \u0421\u041a\u041d\u041e'), (17, '\u0424\u043e\u0440\u043c\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u0441\u0435\u0440\u0432\u0438\u0441\u0430'), (18, '\u0424\u043e\u0440\u043c\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u043e\u0442\u0447\u0435\u0442\u0430'), (19, '\u0412\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0435 \u0438\u0437 \u0430\u0440\u0445\u0438\u0432\u0430'), (20, '\u0421\u0432\u0435\u0440\u043a\u0430 \u0440\u0435\u0435\u0441\u0442\u0440\u0430'), (21, '\u0421\u043d\u0430\u0440\u044f\u0436\u0435\u043d\u0438\u0435 \u0421\u041a\u041e'), (22, '\u0421\u043d\u044f\u0442\u0438\u0435 \u0421\u041a\u041e')]),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='limitedmonitoring',
            name='controlled_action',
            field=postgres.fields.ArrayField(models.SmallIntegerField(choices=[(1, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430'), (3, '\u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435'), (2, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440'), (4, '\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435'), (5, '\u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435'), (6, '\u0412\u044b\u0433\u0440\u0443\u0437\u043a\u0430'), (7, '\u0417\u0430\u0433\u0440\u0443\u0437\u043a\u0430'), (8, '\u0414\u0440\u0443\u0433\u043e\u0435'), (9, '\u0412\u0445\u043e\u0434 \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0443'), (10, '\u0412\u044b\u0445\u043e\u0434 \u0438\u0437 \u0441\u0438\u0441\u0442\u0435\u043c\u044b'), (11, '\u0420\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u044f \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0435'), (12, '\u0414\u043e\u0431\u0430\u0432\u043b\u0435\u043d\u0438\u0435 \u043a\u043e\u043c\u043f\u043b\u0435\u043a\u0442\u0443\u044e\u0449\u0438\u0445 \u0432 \u043f\u0430\u0440\u0442\u0438\u044e'), (13, '\u0421\u043d\u0430\u0440\u044f\u0436\u0435\u043d\u0438\u0435 \u0421\u041a\u041d\u041e'), (14, '\u0423\u0441\u0442\u0430\u043d\u043e\u0432\u043a\u0430 \u0421\u041a\u041d\u041e'), (15, '\u0420\u0430\u0437\u0443\u043a\u043e\u043c\u043f\u043b\u0435\u043a\u0442\u0430\u0446\u0438\u044f \u0421\u041a\u041d\u041e'), (16, '\u0421\u043d\u044f\u0442\u0438\u0435 \u0421\u041a\u041d\u041e'), (17, '\u0424\u043e\u0440\u043c\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u0441\u0435\u0440\u0432\u0438\u0441\u0430'), (18, '\u0424\u043e\u0440\u043c\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u043e\u0442\u0447\u0435\u0442\u0430'), (19, '\u0412\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0435 \u0438\u0437 \u0430\u0440\u0445\u0438\u0432\u0430'), (20, '\u0421\u0432\u0435\u0440\u043a\u0430 \u0440\u0435\u0435\u0441\u0442\u0440\u0430'), (21, '\u0421\u043d\u0430\u0440\u044f\u0436\u0435\u043d\u0438\u0435 \u0421\u041a\u041e'), (22, '\u0421\u043d\u044f\u0442\u0438\u0435 \u0421\u041a\u041e')]), verbose_name='\u0422\u0438\u043f \u043a\u043e\u043d\u0442\u0440\u043e\u043b\u0438\u0440\u0443\u0435\u043c\u044b\u0445 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0439 \u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u044f', size=None),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='limitedmonitoringevent',
            name='controlled_action',
            field=models.SmallIntegerField(verbose_name='\u0422\u0438\u043f \u043a\u043e\u043d\u0442\u0440\u043e\u043b\u0438\u0440\u0443\u0435\u043c\u044b\u0445 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u0439 \u043f\u043e\u043b\u044c\u0437\u043e\u0432\u0430\u0442\u0435\u043b\u044f', choices=[(1, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440 \u0441\u043f\u0438\u0441\u043a\u0430'), (3, '\u0421\u043e\u0437\u0434\u0430\u043d\u0438\u0435'), (2, '\u041f\u0440\u043e\u0441\u043c\u043e\u0442\u0440'), (4, '\u0418\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435'), (5, '\u0423\u0434\u0430\u043b\u0435\u043d\u0438\u0435'), (6, '\u0412\u044b\u0433\u0440\u0443\u0437\u043a\u0430'), (7, '\u0417\u0430\u0433\u0440\u0443\u0437\u043a\u0430'), (8, '\u0414\u0440\u0443\u0433\u043e\u0435'), (9, '\u0412\u0445\u043e\u0434 \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0443'), (10, '\u0412\u044b\u0445\u043e\u0434 \u0438\u0437 \u0441\u0438\u0441\u0442\u0435\u043c\u044b'), (11, '\u0420\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u044f \u0432 \u0441\u0438\u0441\u0442\u0435\u043c\u0435'), (12, '\u0414\u043e\u0431\u0430\u0432\u043b\u0435\u043d\u0438\u0435 \u043a\u043e\u043c\u043f\u043b\u0435\u043a\u0442\u0443\u044e\u0449\u0438\u0445 \u0432 \u043f\u0430\u0440\u0442\u0438\u044e'), (13, '\u0421\u043d\u0430\u0440\u044f\u0436\u0435\u043d\u0438\u0435 \u0421\u041a\u041d\u041e'), (14, '\u0423\u0441\u0442\u0430\u043d\u043e\u0432\u043a\u0430 \u0421\u041a\u041d\u041e'), (15, '\u0420\u0430\u0437\u0443\u043a\u043e\u043c\u043f\u043b\u0435\u043a\u0442\u0430\u0446\u0438\u044f \u0421\u041a\u041d\u041e'), (16, '\u0421\u043d\u044f\u0442\u0438\u0435 \u0421\u041a\u041d\u041e'), (17, '\u0424\u043e\u0440\u043c\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u0441\u0435\u0440\u0432\u0438\u0441\u0430'), (18, '\u0424\u043e\u0440\u043c\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u043e\u0442\u0447\u0435\u0442\u0430'), (19, '\u0412\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0435 \u0438\u0437 \u0430\u0440\u0445\u0438\u0432\u0430'), (20, '\u0421\u0432\u0435\u0440\u043a\u0430 \u0440\u0435\u0435\u0441\u0442\u0440\u0430'), (21, '\u0421\u043d\u0430\u0440\u044f\u0436\u0435\u043d\u0438\u0435 \u0421\u041a\u041e'), (22, '\u0421\u043d\u044f\u0442\u0438\u0435 \u0421\u041a\u041e')]),
            preserve_default=True,
        ),
    ]
