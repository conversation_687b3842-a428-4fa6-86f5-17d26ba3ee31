import datetime

from django import template

from skko_web_core.apps.core.models import CashBox

register = template.Library()


@register.filter
def get_ksa_pk(be):
    qs_cashbox = CashBox.objects.filter(
        trading_object__in=be.trading_objects.all(),
        archived__isnull=True).all()
    ksa_cashbox = len([item for item in qs_cashbox if item.cashbox_type == 0])
    pk_cashbox = len([item for item in qs_cashbox if item.cashbox_type == 1])

    return '{0} / {1}'.format(ksa_cashbox, pk_cashbox)
