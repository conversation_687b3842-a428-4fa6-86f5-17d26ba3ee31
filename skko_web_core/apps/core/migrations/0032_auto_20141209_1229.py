# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations
import skko_web_core.apps.core.models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0031_auto_20141205_1608'),
    ]

    operations = [
        migrations.AddField(
            model_name='cashboxpayment',
            name='month',
            field=models.PositiveSmallIntegerField(null=True, verbose_name='\u041c\u0435\u0441\u044f\u0446, \u0437\u0430 \u043a\u043e\u0442\u043e\u0440\u044b\u0439 \u0432\u044b\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0430 \u043e\u043f\u043b\u0430\u0442\u0430', blank=True),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='cashboxpayment',
            name='year',
            field=models.PositiveSmallIntegerField(null=True, verbose_name='\u0413\u043e\u0434, \u0437\u0430 \u043a\u043e\u0442\u043e\u0440\u044b\u0439 \u0432\u044b\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0430 \u043e\u043f\u043b\u0430\u0442\u0430', blank=True),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='cashboxpayment',
            name='type',
            field=models.PositiveSmallIntegerField(verbose_name='\u0422\u0438\u043f \u043f\u043b\u0430\u0442\u0435\u0436\u0430', choices=[(1, '\u0417\u0430\u043b\u043e\u0433\u043e\u0432\u0430\u044f \u0441\u0442\u043e\u0438\u043c\u043e\u0441\u0442\u044c \u0421\u041a\u041d\u041e'), (2, '\u041e\u043f\u043b\u0430\u0442\u0430 \u0437\u0430 \u043f\u043e\u0434\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u0435'), (3, '\u041e\u043f\u043b\u0430\u0442\u0430 \u0437\u0430 \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u043e\u043d\u043d\u044b\u0435 \u0443\u0441\u043b\u0443\u0433\u0438')]),
        ),
        migrations.AlterField(
            model_name='role',
            name='method',
            field=models.IntegerField(verbose_name='\u041c\u0435\u0442\u043e\u0434 \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u0438\u044f \u043f\u043e\u043b\u043d\u043e\u043c\u043e\u0447\u0438\u0439', choices=[(1, skko_web_core.apps.core.models.TechnicalCenterOrServiceLimitation()), (2, skko_web_core.apps.core.models.SupervisorySubdivisionLimitation()), (3, skko_web_core.apps.core.models.IndividualLimitation()), (4, skko_web_core.apps.core.models.UNPLimitation()), (5, skko_web_core.apps.core.models.Administrator())]),
        ),
    ]
