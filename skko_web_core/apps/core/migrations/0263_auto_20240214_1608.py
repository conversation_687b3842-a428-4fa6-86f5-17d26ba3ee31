# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0262_auto_20240119_1256'),
    ]

    operations = [
        migrations.CreateModel(
            name='PCSInvalidDocument',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('cashbox_number', models.IntegerField(verbose_name='\u0423\u0447\u0435\u0442\u043d\u044b\u0439 \u043d\u043e\u043c\u0435\u0440 \u041f\u041a')),
                ('document', models.TextField(verbose_name='\u041a\u0430\u0441\u0441\u043e\u0432\u044b\u0439 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442 \u0432 \u0444\u043e\u0440\u043c\u0430\u0442\u0435 base64')),
                ('document_date', models.DateTimeField(null=True, verbose_name='\u0414\u0430\u0442\u0430 \u0444\u043e\u0440\u043c\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u044f \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430', blank=True)),
                ('eds', models.TextField(verbose_name='\u0422\u042d\u0426\u041f \u0432\u043b\u0430\u0434\u0435\u043b\u044c\u0446\u0430 \u041f\u041a')),
                ('unique_code', models.CharField(max_length=24, verbose_name='\u0423\u043d\u0438\u043a\u0430\u043b\u044c\u043d\u044b\u0439 \u0438\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440 \u043a\u0430\u0441\u0441\u043e\u0432\u043e\u0433\u043e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430')),
                ('ui', models.DecimalField(verbose_name='\u0423\u043d\u0438\u043a\u0430\u043b\u044c\u043d\u044b\u0439 \u0438\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440', max_digits=30, decimal_places=0)),
                ('eds_operator', models.TextField(verbose_name='\u0422\u042d\u0426\u041f \u043e\u043f\u0435\u0440\u0430\u0442\u043e\u0440\u0430')),
                ('errors', models.TextField(verbose_name='\u0421\u043f\u0438\u0441\u043e\u043a \u043e\u0448\u0438\u0431\u043e\u043a')),
                ('operator_code', models.IntegerField(verbose_name='\u041a\u043e\u0434 \u043e\u043f\u0435\u0440\u0430\u0442\u043e\u0440\u0430 \u0432 \u0410\u0418\u0421 \u041a\u041a\u041e')),
                ('operator_date', models.DateTimeField(verbose_name='\u0414\u0430\u0442\u0430 \u043f\u043e\u0441\u0442\u0443\u043f\u043b\u0435\u043d\u0438\u044f \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 \u043e\u0442 \u041f\u041a \u0432 \u0410\u0418\u0421 \u041f\u041a\u0421')),
                ('operator_unp', models.IntegerField(verbose_name='\u0423\u041d\u041f \u043e\u043f\u0435\u0440\u0430\u0442\u043e\u0440\u0430')),
                ('document_type', models.SmallIntegerField(verbose_name='\u0422\u0438\u043f \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u043e\u0433\u043e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430', choices=[(0, '\u041f\u043b\u0430\u0442\u0451\u0436\u043d\u044b\u0439 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)'), (1, '\u0412\u043d\u0435\u0441\u0435\u043d\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)'), (2, '\u0418\u0437\u044a\u044f\u0442\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)'), (3, '\u0412\u043e\u0437\u0432\u0440\u0430\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)'), (4, '\u0410\u043d\u043d\u0443\u043b\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)'), (6, '\u0414\u043e\u043a\u0443\u043c\u0435\u043d\u0442 \u043d\u0435\u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u043d\u043e\u0439 \u0441\u0442\u0440\u0443\u043a\u0442\u0443\u0440\u044b'), (10, '\u041f\u043b\u0430\u0442\u0451\u0436\u043d\u044b\u0439 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (11, '\u0412\u043d\u0435\u0441\u0435\u043d\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (12, '\u0418\u0437\u044a\u044f\u0442\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (13, '\u0412\u043e\u0437\u0432\u0440\u0430\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (14, '\u0410\u043d\u043d\u0443\u043b\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (15, 'Z-\u043e\u0442\u0447\u0435\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (16, '\u0412\u044b\u0434\u0430\u0447\u0430 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (20, 'Z-\u043e\u0442\u0447\u0435\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)')])),
                ('document_number', models.IntegerField(null=True, verbose_name='\u041d\u043e\u043c\u0435\u0440 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u043e\u0433\u043e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430', blank=True)),
            ],
            options={
                'verbose_name': '\u041a\u0434 \u043d\u0435 \u043f\u0440\u043e\u0448\u0435\u0434\u0448\u0438\u0435 \u0424\u041b\u041a',
                'managed': False,
            },
            bases=(models.Model,),
        ),
        migrations.AlterField(
            model_name='corruptedpackagecounts',
            name='cr_type',
            field=models.IntegerField(blank=True, null=True, verbose_name='\u0422\u0438\u043f \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430', choices=[(0, '\u041f\u043b\u0430\u0442\u0451\u0436\u043d\u044b\u0439 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)'), (1, '\u0412\u043d\u0435\u0441\u0435\u043d\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)'), (2, '\u0418\u0437\u044a\u044f\u0442\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)'), (3, '\u0412\u043e\u0437\u0432\u0440\u0430\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)'), (4, '\u0410\u043d\u043d\u0443\u043b\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)'), (6, '\u0414\u043e\u043a\u0443\u043c\u0435\u043d\u0442 \u043d\u0435\u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u043d\u043e\u0439 \u0441\u0442\u0440\u0443\u043a\u0442\u0443\u0440\u044b'), (10, '\u041f\u043b\u0430\u0442\u0451\u0436\u043d\u044b\u0439 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (11, '\u0412\u043d\u0435\u0441\u0435\u043d\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (12, '\u0418\u0437\u044a\u044f\u0442\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (13, '\u0412\u043e\u0437\u0432\u0440\u0430\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (14, '\u0410\u043d\u043d\u0443\u043b\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (15, 'Z-\u043e\u0442\u0447\u0435\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (16, '\u0412\u044b\u0434\u0430\u0447\u0430 (\u0444\u043e\u0440\u043c\u0430\u0442 2023)'), (20, 'Z-\u043e\u0442\u0447\u0435\u0442 (\u0444\u043e\u0440\u043c\u0430\u0442 2015)')]),
            preserve_default=True,
        ),
    ]
