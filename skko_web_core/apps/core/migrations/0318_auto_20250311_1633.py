# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0317_tradingobjectrequest'),
    ]

    operations = [
        migrations.AddField(
            model_name='tradingobjectrequest',
            name='status',
            field=models.SmallIntegerField(default=1, verbose_name='\u0421\u0442\u0430\u0442\u0443\u0441 \u0437\u0430\u044f\u0432\u043a\u0438'),
            preserve_default=True,
        ),
        migrations.AddField(
            model_name='tradingobjectrequest',
            name='status_note',
            field=models.TextField(default=1, verbose_name='\u0414\u0435\u0442\u0430\u043b\u0438\u0437\u0430\u0446\u0438\u044f \u0441\u0442\u0430\u0442\u0443\u0441\u0430 \u0437\u0430\u044f\u0432\u043a\u0438'),
            preserve_default=False,
        ),
    ]
