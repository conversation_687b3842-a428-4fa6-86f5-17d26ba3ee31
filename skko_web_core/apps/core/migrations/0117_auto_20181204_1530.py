# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations
import django.db.models.deletion
import datetime
import skko_web_core.apps.core.models
from django.utils.translation import ugettext_lazy as _

class Migration(migrations.Migration):

    dependencies = [
        ('core', '0116_auto_20181203_1247'),
    ]

    operations = [
        migrations.CreateModel(
            name='AdditionalAgreementWithPCS',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('add_type', models.IntegerField(verbose_name='\u0422\u0438\u043f \u0434\u043e\u043f. \u0441\u043e\u0433\u043b\u0430\u0448\u0435\u043d\u0438\u044f', choices=[(1, '\u043f\u0440\u043e\u0434\u043b\u0435\u043d\u0438\u0435 \u0441\u0440\u043e\u043a\u0430'), (2, '\u0438\u0437\u043c\u0435\u043d\u0435\u043d\u0438\u0435 \u043a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u0430 \u041a\u041e'), (3, '\u043f\u0440\u043e\u0447\u0438\u0435')])),
                ('number', models.CharField(max_length=20, verbose_name='\u041d\u043e\u043c\u0435\u0440')),
                ('date', models.DateField(verbose_name='\u0414\u0430\u0442\u0430')),
                ('note', models.CharField(max_length=255, verbose_name='\u041f\u0440\u0438\u043c\u0435\u0447\u0430\u043d\u0438\u0435', blank=True)),
            ],
            options={
                'verbose_name': _('AdditionalAgreementWithPCS'),
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='AgreementWithPCS',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('number', models.CharField(max_length=20, verbose_name='\u2116 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430')),
                ('valid_from', models.DateField(verbose_name='\u0421\u0440\u043e\u043a \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u044f \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430 \u0441')),
                ('valid_to', models.DateField(verbose_name='\u043f\u043e')),
                ('extension', models.IntegerField(verbose_name='\u041f\u0440\u043e\u0434\u043b\u0435\u043d\u0438\u0435 \u0441\u0440\u043e\u043a\u0430 \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430', choices=[(1, '\u0410\u0432\u0442\u043e\u043c\u0430\u0442\u0438\u0447\u0435\u0441\u043a\u0438'), (2, '\u0414\u043e\u043f. \u0441\u043e\u0433\u043b\u0430\u0448\u0435\u043d\u0438\u0435\u043c')])),
                ('cancel_date', models.DateField(null=True, verbose_name='\u0414\u0430\u0442\u0430 \u0440\u0430\u0441\u0442\u043e\u0440\u0436\u0435\u043d\u0438\u044f \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430', blank=True)),
                ('note', models.CharField(max_length=255, verbose_name='\u041f\u0440\u0438\u043c\u0435\u0447\u0430\u043d\u0438\u0435', blank=True)),
                ('last_update_by_be', models.BooleanField(default=False, verbose_name='\u0414\u0430\u043d\u043d\u044b\u0435 \u0431\u044b\u043b\u0438 \u0438\u0437\u043c\u0435\u043d\u0435\u043d\u044b/\u0434\u043e\u0431\u0430\u0432\u043b\u0435\u043d\u044b \u0421\u041f\u0414')),
                ('business_entity', models.ForeignKey(related_name='agreements_with_pcs', on_delete=django.db.models.deletion.PROTECT, verbose_name='\u0421\u041f\u0414', to='core.BusinessEntity')),
                ('center', models.ForeignKey(related_name='agreements_with_pcs', on_delete=django.db.models.deletion.PROTECT, verbose_name='\u041e\u041f\u041a\u0421', to='core.PCSOperator')),
            ],
            options={
                'verbose_name': '\u0414\u043e\u0433\u043e\u0432\u043e\u0440 \u0441 \u043e\u043f\u0435\u0440\u0430\u0442\u043e\u0440\u043e\u043c \u041f\u041a\u0421',
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='AgreementWithPCSScan',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('name', models.CharField(max_length=255, verbose_name='\u041d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435')),
                ('url', models.CharField(max_length=255, verbose_name='\u0421\u0441\u044b\u043b\u043a\u0430 \u043d\u0430 \u0441\u043a\u0430\u043d')),
                ('thumbnail_url', models.CharField(max_length=255, verbose_name='\u0421\u0441\u044b\u043b\u043a\u0430 \u043d\u0430 \u044d\u0441\u043a\u0438\u0437 \u0441\u043a\u0430\u043d')),
                ('agreement', models.ForeignKey(related_name='scans_with_pcs', verbose_name='\u0414\u043e\u0433\u043e\u0432\u043e\u0440', to='core.AgreementWithPCS')),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.AddField(
            model_name='additionalagreementwithpcs',
            name='agreement',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, verbose_name='\u0414\u043e\u0433\u043e\u0432\u043e\u0440', to='core.AgreementWithPCS'),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='pcsoperatoradditionalagreement',
            name='sign_date',
            field=models.DateField(default=datetime.date(2018, 12, 4), verbose_name='\u0414\u0430\u0442\u0430 \u0437\u0430\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u044f'),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='pcsoperatoragreement',
            name='sign_date',
            field=models.DateField(default=datetime.date(2018, 12, 4), verbose_name='\u0414\u0430\u0442\u0430 \u0437\u0430\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u044f \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0430'),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='pcsoperatorconclusion',
            name='conclusion_date',
            field=models.DateField(default=datetime.date(2018, 12, 4), verbose_name='\u0414\u0430\u0442\u0430 \u0437\u0430\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u044f'),
            preserve_default=True,
        ),
        migrations.AlterField(
            model_name='role',
            name='method',
            field=models.IntegerField(verbose_name='\u041c\u0435\u0442\u043e\u0434 \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u0438\u044f \u043f\u043e\u043b\u043d\u043e\u043c\u043e\u0447\u0438\u0439', choices=[(1, skko_web_core.apps.core.models.TechnicalCenterOrServiceLimitation()), (2, skko_web_core.apps.core.models.SupervisorySubdivisionLimitation()), (3, skko_web_core.apps.core.models.IndividualLimitation()), (4, skko_web_core.apps.core.models.UNPLimitation()), (5, skko_web_core.apps.core.models.Administrator())]),
            preserve_default=True,
        ),
    ]
