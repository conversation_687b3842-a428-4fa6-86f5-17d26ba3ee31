# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0264_additionalagreementwithpcsscan'),
    ]

    operations = [
        migrations.CreateModel(
            name='PCSOperatorAdditAgreementScan',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('name', models.CharField(max_length=255, verbose_name='\u041d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435')),
                ('url', models.CharField(max_length=255, verbose_name='\u0421\u0441\u044b\u043b\u043a\u0430 \u043d\u0430 \u0441\u043a\u0430\u043d')),
                ('thumbnail_url', models.Char<PERSON>ield(max_length=255, verbose_name='\u0421\u0441\u044b\u043b\u043a\u0430 \u043d\u0430 \u044d\u0441\u043a\u0438\u0437 \u0441\u043a\u0430\u043d')),
                ('agreement', models.ForeignKey(related_name='scans', verbose_name='\u0414\u043e\u0433\u043e\u0432\u043e\u0440', to='core.PCSOperatorAdditionalAgreement')),
            ],
            options={
                'verbose_name': '\u0421\u043a\u0430\u043d \u0434\u043e\u043f. \u0441\u043e\u0433\u043b\u0430\u0448\u0435\u043d\u0438\u044f \u043a \u0434\u043e\u0433\u043e\u0432\u043e\u0440\u0443 \u041f\u041a\u0421 \u0441 \u0410\u0418\u0421 \u041a\u041a\u041e',
            },
            bases=(models.Model,),
        ),
        migrations.RemoveField(
            model_name='additionalagreementwithpcsscan',
            name='agreement',
        ),
        migrations.DeleteModel(
            name='AdditionalAgreementWithPCSScan',
        ),
    ]
