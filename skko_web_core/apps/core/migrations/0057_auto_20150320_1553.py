# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations
import django.db.models.deletion
import skko_web_core.apps.core.models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0056_auto_20150227_1629'),
    ]

    operations = [
        migrations.CreateModel(
            name='TradeItem',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('tradeitem_id', models.IntegerField(verbose_name='ID')),
                ('gtin', models.CharField(max_length=14, verbose_name='GTIN')),
                ('functional_name', models.CharField(max_length=70, null=True, verbose_name='\u0424\u0443\u043d\u043a\u0446\u0438\u043e\u043d\u0430\u043b\u044c\u043d\u043e\u0435 \u043d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435 \u0443\u0434\u0438\u043d\u0438\u0446\u044b \u0442\u043e\u0432\u0430\u0440\u0430 (\u043f\u0440\u043e\u0434\u0443\u043a\u0446\u0438\u0438)')),
                ('brand_name', models.CharField(max_length=70, null=True, verbose_name='\u0411\u0440\u0435\u043d\u0434 (\u0442\u043e\u0440\u0433\u043e\u0432\u0430\u044f \u043c\u0430\u0440\u043a\u0430)')),
                ('subbrand', models.CharField(max_length=70, null=True, verbose_name='\u0421\u0443\u0431\u0431\u0440\u0435\u043d\u0434')),
                ('variant', models.CharField(max_length=140, null=True, verbose_name='\u0420\u0430\u0437\u043d\u043e\u0432\u0438\u0434\u043d\u043e\u0441\u0442\u044c')),
                ('addinfo', models.TextField(null=True, verbose_name='\u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u044b\u0435 \u0441\u0432\u0435\u0434\u0435\u043d\u0438\u044f')),
                ('unit_of_measure', models.CharField(max_length=30, null=True, verbose_name='\u0415\u0434\u0438\u043d\u0438\u0446\u0430 \u0438\u0437\u043c\u0435\u0440\u0435\u043d\u0438\u044f \u0442\u043e\u0432\u0430\u0440\u0430 (\u0432 \u0443\u043f\u0430\u043a\u043e\u0432\u043a\u0435)', choices=[('MM', '\u041c\u0438\u043b\u043b\u0438\u043c\u0435\u0442\u0440'), ('MR', '\u041c\u0435\u0442\u0440'), ('ME', '\u041c\u0438\u043b\u043b\u0438\u0433\u0440\u0430\u043c\u043c'), ('GR', '\u0413\u0440\u0430\u043c\u043c'), ('RG', '\u041a\u0438\u043b\u043e\u0433\u0440\u0430\u043c\u043c'), ('ML', '\u041c\u0438\u043b\u043b\u0438\u043b\u0438\u0442\u0440'), ('LT', '\u041b\u0438\u0442\u0440'), ('CC', '\u041a\u0443\u0431\u0438\u0447\u0435\u0441\u043a\u0438\u0439 \u0441\u0430\u043d\u0442\u0438\u043c\u0435\u0442\u0440'), ('C8', '\u041a\u0443\u0431\u0438\u0447\u0435\u0441\u043a\u0438\u0439 \u0434\u0435\u0446\u0438\u043c\u0435\u0442\u0440'), ('CR', '\u041a\u0443\u0431\u0438\u0447\u0435\u0441\u043a\u0438\u0439 \u043c\u0435\u0442\u0440'), ('SM', '\u041a\u0432\u0430\u0434\u0440\u0430\u0442\u043d\u044b\u0439 \u043c\u0435\u0442\u0440'), ('1N', '\u041e\u0434\u043d\u0430 \u0448\u0442\u0443\u043a\u0430'), ('PR', '\u041f\u0430\u0440\u0430')])),
                ('net_content', models.CharField(max_length=17, null=True, verbose_name='\u041a\u043e\u043b\u0438\u0447\u0435\u0441\u0442\u0432\u043e \u0442\u043e\u0432\u0430\u0440\u0430 (\u0432 \u0443\u043f\u0430\u043a\u043e\u0432\u043a\u0435)')),
                ('okrb007_code', models.CharField(max_length=20, null=True, verbose_name='\u041a\u043e\u0434 \u0442\u043e\u0432\u0430\u0440\u0430 \u043f\u043e \u041e\u041a\u0420\u0411-007')),
                ('tnved_code', models.CharField(max_length=13, null=True, verbose_name='\u041a\u043e\u0434 \u0422\u041d\u0412\u042d\u0414')),
                ('brick_code', models.CharField(max_length=8, null=True, verbose_name='\u041a\u043e\u0434 \u0431\u0440\u0438\u043a\u0430 \u043f\u043e GPC')),
                ('information_provider_gln', models.CharField(max_length=13, null=True, verbose_name='GLN \u043f\u0440\u043e\u0438\u0437\u0432\u043e\u0434\u0438\u0442\u0435\u043b\u044f')),
                ('information_provider_name', models.CharField(max_length=70, verbose_name='\u041d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435 \u043f\u0440\u043e\u0438\u0437\u0432\u043e\u0434\u0438\u0442\u0435\u043b\u044f')),
                ('gln', models.CharField(max_length=13, verbose_name='GLN \u043f\u0440\u0435\u0434\u043e\u0441\u0442\u0430\u0432\u0438\u0442\u0435\u043b\u044f \u0438\u043d\u0444\u043e\u0440\u043c\u0430\u0446\u0438\u0438')),
                ('dateflag', models.DateField(null=True, verbose_name='\u0414\u0430\u0442\u0430 \u0431\u043b\u043e\u043a\u0438\u0440\u043e\u0432\u043a\u0438 GTIN', blank=True)),
            ],
            options={
            },
            bases=(models.Model,),
        ),
        migrations.CreateModel(
            name='MailUser',
            fields=[
            ],
            options={
                'db_table': 'users',
                'managed': False,
            },
            bases=(models.Model,),
        ),
        migrations.AlterField(
            model_name='metricscount',
            name='enter_count',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='role',
            name='method',
            field=models.IntegerField(verbose_name='\u041c\u0435\u0442\u043e\u0434 \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u0438\u044f \u043f\u043e\u043b\u043d\u043e\u043c\u043e\u0447\u0438\u0439', choices=[(1, skko_web_core.apps.core.models.TechnicalCenterOrServiceLimitation()), (2, skko_web_core.apps.core.models.SupervisorySubdivisionLimitation()), (3, skko_web_core.apps.core.models.IndividualLimitation()), (4, skko_web_core.apps.core.models.UNPLimitation()), (5, skko_web_core.apps.core.models.Administrator())]),
        ),
        migrations.AlterField(
            model_name='user',
            name='role',
            field=models.ForeignKey(related_name='users', on_delete=django.db.models.deletion.PROTECT, verbose_name='\u0420\u043e\u043b\u044c', blank=True, to='core.Role', null=True),
        ),
    ]
