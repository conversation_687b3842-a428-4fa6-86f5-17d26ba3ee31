# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations
import jsonfield.fields
import skko_web_core.apps.core.models
import datetime


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0159_auto_20191213_1720'),
    ]

    operations = [
        # migrations.DeleteModel(
        #     name='RefundOperation',
        # ),
        migrations.CreateModel(
            name='RefundOperation',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('cashbox_number', models.PositiveIntegerField(verbose_name='\u0420\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u043e\u043d\u043d\u044b\u0439 \u043d\u043e\u043c\u0435\u0440 \u041a\u041e \u0432 \u0410\u0418\u0421 \u041a\u041a\u041e')),
                ('factory_number', models.CharField(max_length=20, verbose_name='\u0421\u0435\u0440\u0438\u0439\u043d\u044b\u0439 \u043d\u043e\u043c\u0435\u0440 \u0421\u041a\u041d\u041e')),
                ('cashier', models.CharField(max_length=16, verbose_name='\u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440 \u043a\u0430\u0441\u0441\u0438\u0440\u0430')),
                ('number', models.PositiveIntegerField(verbose_name='\u041d\u043e\u043c\u0435\u0440 \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438')),
                ('issued_at', models.DateTimeField(verbose_name='\u0414\u0430\u0442\u0430, \u0432\u0440\u0435\u043c\u044f \u043e\u0444\u043e\u0440\u043c\u043b\u0435\u043d\u0438\u044f \u043e\u043f\u0435\u0440\u0430\u0446\u0438\u0438')),
                ('passed_at', models.DateTimeField(verbose_name='\u0414\u0430\u0442\u0430, \u0432\u0440\u0435\u043c\u044f \u043f\u0435\u0440\u0435\u0434\u0430\u0447\u0438 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u043e\u0433\u043e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 \u0432 \u0421\u041a\u041d\u041e')),
                ('created_at', models.DateTimeField(verbose_name='\u0414\u0430\u0442\u0430, \u0432\u0440\u0435\u043c\u044f \u043f\u0435\u0440\u0435\u0434\u0430\u0447\u0438 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u043e\u0433\u043e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 \u0432 \u0426\u041e\u0414')),
                ('currency', models.CharField(max_length=3, verbose_name='\u041d\u0430\u0438\u043c\u0435\u043d\u043e\u0432\u0430\u043d\u0438\u0435 (\u043a\u043e\u0434) \u0432\u0430\u043b\u044e\u0442\u044b')),
                ('tag', models.PositiveSmallIntegerField(verbose_name='\u041f\u0440\u0438\u0437\u043d\u0430\u043a \u043f\u0440\u0438\u043d\u0430\u0434\u043b\u0435\u0436\u043d\u043e\u0441\u0442\u0438 \u043f\u043e\u0437\u0438\u0446\u0438\u0438')),
                ('ean', models.BigIntegerField(verbose_name='\u041a\u043e\u0434 \u0442\u043e\u0432\u0430\u0440\u0430/\u0423\u0441\u043b\u0443\u0433\u0438 EAN13')),
                ('count', models.DecimalField(verbose_name='\u041a\u043e\u043b-\u0432\u043e \u0442\u043e\u0432\u0430\u0440\u0430/\u0443\u0441\u043b\u0443\u0433\u0438', max_digits=8, decimal_places=3)),
                ('amount', models.DecimalField(verbose_name='\u0421\u0443\u043c\u043c\u0430', max_digits=100, decimal_places=2)),
                ('cash_amount', models.DecimalField(verbose_name='\u0421\u0443\u043c\u043c\u0430 \u0432\u043e\u0437\u0432\u0440\u0430\u0442\u0430 \u043d\u0430\u043b\u0438\u0447\u043d\u044b\u043c\u0438', max_digits=100, decimal_places=2)),
                ('clearing_amount', models.DecimalField(verbose_name='\u0421\u0443\u043c\u043c\u0430 \u0432\u043e\u0437\u0432\u0440\u0430\u0442\u0430 \u043f\u043e \u0431\u0435\u0437\u043d\u0430\u043b\u0438\u0447\u043d\u043e\u043c\u0443 \u0440\u0430\u0441\u0447\u0435\u0442\u0443', max_digits=100, decimal_places=2)),
                ('unique_code', models.CharField(max_length=24, verbose_name='\u0423\u043d\u0438\u043a\u0430\u043b\u044c\u043d\u044b\u0439 \u043a\u043e\u0434 \u0421\u041a\u0417\u0418')),
                ('special_eds', models.TextField(verbose_name='\u0421\u043f\u0435\u0446\u0438\u0430\u043b\u044c\u043d\u0430\u044f \u0447\u0430\u0441\u0442\u044c(\u042d\u0426\u041f)')),
                ('crypto_counter', models.BigIntegerField(verbose_name='\u0421\u0447\u0435\u0442\u0447\u0438\u043a \u0421\u041a\u0417\u0418')),
                ('extra', jsonfield.fields.JSONField(verbose_name='\u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u043e\u0435 \u0440\u0435\u0437\u0435\u0440\u0432\u043e\u043d\u043e\u0435 \u043f\u043e\u043b\u0435')),
                ('status', models.SmallIntegerField(verbose_name='\u0421\u0442\u0430\u0442\u0443\u0441 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u043e\u0433\u043e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 \u0432 \u0426\u041e\u0414 \u0410\u0418\u0421 \u041a\u041a\u041e')),
                ('status_note', models.TextField(verbose_name='\u041f\u0440\u0438\u043c\u0435\u0447\u0430\u043d\u0438\u0435 \u043e \u0441\u043c\u0435\u043d\u0435 \u0441\u0442\u0430\u0442\u0443\u0441\u0430 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u043e\u0433\u043e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 \u0432 \u0426\u041e\u0414 \u0410\u0418\u0421 \u041a\u041a\u041e')),
            ],
            options={
                'managed': False,
            },
            bases=(models.Model, skko_web_core.apps.core.models.ReplacedInterface),
        ),
    ]
