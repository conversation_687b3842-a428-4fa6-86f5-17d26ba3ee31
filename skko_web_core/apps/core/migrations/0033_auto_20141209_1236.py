# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations
import skko_web_core.apps.core.models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0032_auto_20141209_1229'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cashboxpayment',
            name='month',
            field=models.PositiveSmallIntegerField(blank=True, null=True, verbose_name='\u041c\u0435\u0441\u044f\u0446, \u0437\u0430 \u043a\u043e\u0442\u043e\u0440\u044b\u0439 \u0432\u044b\u0441\u0442\u0430\u0432\u043b\u0435\u043d\u0430 \u043e\u043f\u043b\u0430\u0442\u0430', choices=[(1, 'January'), (2, 'February'), (3, 'March'), (4, 'April'), (5, 'May'), (6, 'June'), (7, 'July'), (8, 'August'), (9, 'September'), (10, 'October'), (11, 'November'), (12, 'December')]),
        ),
        migrations.AlterField(
            model_name='role',
            name='method',
            field=models.IntegerField(verbose_name='\u041c\u0435\u0442\u043e\u0434 \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u0438\u044f \u043f\u043e\u043b\u043d\u043e\u043c\u043e\u0447\u0438\u0439', choices=[(1, skko_web_core.apps.core.models.TechnicalCenterOrServiceLimitation()), (2, skko_web_core.apps.core.models.SupervisorySubdivisionLimitation()), (3, skko_web_core.apps.core.models.IndividualLimitation()), (4, skko_web_core.apps.core.models.UNPLimitation()), (5, skko_web_core.apps.core.models.Administrator())]),
        ),
    ]
