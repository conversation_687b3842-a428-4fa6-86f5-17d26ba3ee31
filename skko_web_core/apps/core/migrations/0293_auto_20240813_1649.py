# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0292_merge'),
    ]

    operations = [
        migrations.AddField(
            model_name='corruptedpackagecounts',
            name='processing',
            field=models.SmallIntegerField(default=0, verbose_name='\u041e\u0431\u0440\u0430\u0431\u043e\u0442\u043a\u0430 (\u0440\u0430\u0441\u043f\u043e\u0437\u043d\u0430\u0432\u0430\u043d\u0438\u0435)', choices=[(0, '\u041d\u0435\u0432\u043e\u0437\u043c\u043e\u0436\u043d\u0430'), (1, '\u0412\u043e\u0437\u043c\u043e\u0436\u043d\u0430')]),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='corruptedpackagecounts',
            name='source_type',
            field=models.SmallIntegerField(blank=True, null=True, verbose_name='\u0422\u0438\u043f \u0438\u0441\u0442\u043e\u0447\u043d\u0438\u043a\u0430', choices=[(0, '\u041a\u0421\u0410'), (1, '\u041f\u041a'), (2, '\u0410\u041f\u0418 \u043c\u0430\u0440\u043a\u0438\u0440\u043e\u0432\u043e\u043a')]),
            preserve_default=True,
        ),
    ]
