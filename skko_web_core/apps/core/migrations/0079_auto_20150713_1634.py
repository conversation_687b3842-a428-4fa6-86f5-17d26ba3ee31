# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations
import skko_web_core.apps.core.models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0078_auto_20150701_1242'),
    ]

    operations = [
        migrations.AlterField(
            model_name='role',
            name='method',
            field=models.IntegerField(verbose_name='\u041c\u0435\u0442\u043e\u0434 \u043e\u043f\u0440\u0435\u0434\u0435\u043b\u0435\u043d\u0438\u044f \u043f\u043e\u043b\u043d\u043e\u043c\u043e\u0447\u0438\u0439', choices=[(1, skko_web_core.apps.core.models.TechnicalCenterOrServiceLimitation()), (2, skko_web_core.apps.core.models.SupervisorySubdivisionLimitation()), (3, skko_web_core.apps.core.models.IndividualLimitation()), (4, skko_web_core.apps.core.models.UNPLimitation()), (5, skko_web_core.apps.core.models.Administrator())]),
            preserve_default=True,
        ),
    ]
