# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations
import skko_web_core.apps.core.models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0082_auto_20151102_1613'),
    ]
    operations = [
        migrations.AddField(
            model_name='cashbox',
            name='data_mode',
            field=models.IntegerField(default=0, verbose_name='\u0420\u0435\u0436\u0438\u043c \u043f\u0435\u0440\u0435\u0434\u0430\u0447\u0438 \u0434\u0430\u043d\u043d\u044b\u0445', choices=[(0, 'ON-Line'), (1, '\u041f\u043e \u0441\u043e\u0431\u044b\u0442\u0438\u044e'), (2, 'OFF-Line'), (3, '\u041f\u043e Z-\u043e\u0442\u0447\u0451\u0442\u0443')]),
            preserve_default=True,
        ),
    ]
