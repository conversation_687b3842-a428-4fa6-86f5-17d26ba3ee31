# -*- coding: utf-8 -*-
from __future__ import unicode_literals

from django.db import models, migrations
import jsonfield.fields
import skko_web_core.apps.core.models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0227_auto_20230912_1213'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmergencyEvent',
            fields=[
                ('id', models.AutoField(verbose_name='ID', serialize=False, auto_created=True, primary_key=True)),
                ('factory_number', models.CharField(max_length=20, verbose_name='\u0421\u0435\u0440\u0438\u0439\u043d\u044b\u0439 \u043d\u043e\u043c\u0435\u0440 \u0421\u041a\u041d\u041e')),
                ('cashbox_number', models.PositiveIntegerField(null=True, verbose_name='\u0420\u0435\u0433\u0438\u0441\u0442\u0440\u0430\u0446\u0438\u043e\u043d\u043d\u044b\u0439 \u043d\u043e\u043c\u0435\u0440 \u041a\u041e \u0432 \u0410\u0418\u0421 \u041a\u041a\u041e', blank=True)),
                ('event_id', models.IntegerField(verbose_name='ID \u0441\u043e\u0431\u044b\u0442\u0438\u044f', choices=[(1, '\u0412\u0445\u043e\u0434 \u0432 \u0440\u0435\u0436\u0438\u043c \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u0421\u041a\u041d\u041e'), (2, '\u0412\u044b\u0445\u043e\u0434 \u0438\u0437 \u0440\u0435\u0436\u0438\u043c\u0430 \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u0421\u041a\u041d\u041e'), (3, '\u0411\u043b\u043e\u043a\u0438\u0440\u043e\u0432\u043a\u0430 \u043a\u0430\u0441\u0441\u043e\u0432\u043e\u0433\u043e \u043e\u0431\u043e\u0440\u0443\u0434\u043e\u0432\u0430\u043d\u0438\u044f'), (4, '\u0420\u0435\u0437\u0435\u0440\u0432'), (5, '\u0412\u0440\u0435\u043c\u044f \u043e\u043a\u043e\u043d\u0447\u0430\u043d\u0438\u044f \u0441\u0440\u043e\u043a\u0430 \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u044f \u0441\u0435\u0440\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u0430 \u0421\u041a\u0417\u0418'), (6, '\u041c\u043e\u0434\u0443\u043b\u044c \u0421\u041a\u0417\u0418 \u043d\u0435 \u043e\u0442\u0432\u0435\u0447\u0430\u0435\u0442'), (7, '\u041e\u0448\u0438\u0431\u043a\u0430 \u0443\u0447\u0435\u0442\u043d\u044b\u0445 \u0434\u0430\u043d\u043d\u044b\u0445 \u043c\u043e\u0434\u0443\u043b\u044f \u0421\u041a\u0417\u0418'), (8, '\u0420\u0435\u0437\u0435\u0440\u0432'), (9, '\u0420\u0435\u0437\u0435\u0440\u0432'), (10, '\u0412\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u0435 \u0421\u041a\u041d\u041e'), (11, '\u0412\u044b\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u0435 \u0421\u041a\u041d\u041e'), (12, '\u041d\u0430\u043f\u0440\u044f\u0436\u0435\u043d\u0438\u0435 \u0421\u041a\u041d\u041e \u043d\u0438\u0436\u0435 \u0434\u043e\u043f\u0443\u0441\u0442\u0438\u043c\u043e\u0433\u043e (5\u0412 - 5% = < 4,75\u0412)'), (13, '\u041d\u0430\u043f\u0440\u044f\u0436\u0435\u043d\u0438\u0435 \u0421\u041a\u041d\u041e \u0432\u044b\u0448\u0435 \u0434\u043e\u043f\u0443\u0441\u0442\u0438\u043c\u043e\u0433\u043e (5\u0412 + 5% = > 5,25\u0412)'), (14, '\u041e\u0448\u0438\u0431\u043a\u0430 \u0438\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0446\u0438\u0438 \u041a\u0421\u0410 \u0441 \u0421\u041a\u041d\u041e'), (15, '\u041f\u043e\u0442\u0435\u0440\u044f \u0441\u0432\u044f\u0437\u0438 \u0441 \u0421\u041a\u041d\u041e'), (16, '\u0412\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0435 \u0441\u0432\u044f\u0437\u0438 \u0441 \u0421\u041a\u041d\u041e'), (17, '\u0420\u0435\u0437\u0435\u0440\u0432'), (18, '\u041f\u0440\u0435\u0432\u044b\u0448\u0435\u043d \u0440\u0430\u0437\u043c\u0435\u0440 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 (\u0440\u0430\u0437\u043c\u0435\u0440 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 \u0431\u043e\u043b\u0435\u0435 20 \u041a\u0431\u0430\u0439\u0442)'), (19, '\u041e\u0448\u0438\u0431\u043a\u0430 \u0421\u041a\u041d\u041e'), (20, '\u0412\u043e\u0441\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u0438\u0435 \u0421\u041a\u041d\u041e \u043f\u043e\u0441\u043b\u0435 \u043e\u0448\u0438\u0431\u043a\u0438 '), (21, '\u041d\u0438\u0437\u043a\u0438\u0439 \u0443\u0440\u043e\u0432\u0435\u043d\u044c \u0441\u0438\u0433\u043d\u0430\u043b\u0430 \u0441\u0432\u044f\u0437\u0438 GSM (<5 ASU)'), (22, '\u0423\u0441\u0442\u0430\u043d\u043e\u0432\u043b\u0435\u043d\u043e \u043f\u043e\u0434\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u0435 \u043a \u041a\u041c\u0421'), (23, '\u0420\u0435\u0437\u0435\u0440\u0432'), (24, '\u0421\u043e\u0431\u044b\u0442\u0438\u0435 \u0442\u0435\u0441\u0442\u043e\u0432\u043e\u0433\u043e \u041a\u041c\u0421. \u0422\u0435\u0441\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u0421\u041a\u041d\u041e.'), (25, '\u0421\u043e\u0431\u044b\u0442\u0438\u0435 \u0442\u0435\u0441\u0442\u043e\u0432\u043e\u0433\u043e \u041a\u041c\u0421. \u041e\u0448\u0438\u0431\u043a\u0430 \u043f\u0440\u043e\u0432\u0435\u0440\u043a\u0438 \u042d\u0426\u041f \u0434\u043b\u044f \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430'), (26, '\u0421\u043c\u0435\u043d\u0430 \u043e\u0442\u043a\u0440\u044b\u0442\u0430'), (27, '\u0421\u043c\u0435\u043d\u0430 \u0437\u0430\u043a\u0440\u044b\u0442\u0430'), (1001, '\u0412\u0445\u043e\u0434 \u0432 \u0440\u0435\u0436\u0438\u043c \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0438 \u0421\u041a\u041e '), (1002, '\u041e\u0447\u0438\u0441\u0442\u043a\u0430 \u043f\u0430\u043c\u044f\u0442\u0438 \u0421\u041a\u041e'), (1005, '\u0412\u0440\u0435\u043c\u044f \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u0438\u044f \u0434\u0435\u0439\u0441\u0442\u0432\u0438\u044f \u0422\u0421\u041e\u041a'), (1006, '\u041c\u043e\u0434\u0443\u043b\u044c \u0421\u041a\u0417\u0418 \u0421\u041a\u041e \u043d\u0435 \u043e\u0442\u0432\u0435\u0447\u0430\u0435\u0442'), (1007, '\u041e\u0448\u0438\u0431\u043a\u0430 \u0443\u0447\u0435\u0442\u043d\u044b\u0445 \u0434\u0430\u043d\u043d\u044b\u0445 \u043c\u043e\u0434\u0443\u043b\u044f \u0421\u041a\u0417\u0418 \u0421\u041a\u041e'), (1010, '\u0412\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u0435 \u0421\u041a\u041e '), (1011, '\u0412\u044b\u043a\u043b\u044e\u0447\u0435\u043d\u0438\u0435 \u0421\u041a\u041e '), (1012, '\u041d\u0430\u043f\u0440\u044f\u0436\u0435\u043d\u0438\u0435 \u0421\u041a\u041e \u043d\u0438\u0436\u0435 \u0434\u043e\u043f\u0443\u0441\u0442\u0438\u043c\u043e\u0433\u043e (5\u0412 - 5% = < 4,75\u0412)'), (1013, '\u041d\u0430\u043f\u0440\u044f\u0436\u0435\u043d\u0438\u0435 \u0421\u041a\u041e \u0432\u044b\u0448\u0435 \u0434\u043e\u043f\u0443\u0441\u0442\u0438\u043c\u043e\u0433\u043e 5\u0412 + 5% = > 5,25\u0412)'), (1014, '\u041e\u0448\u0438\u0431\u043a\u0430 \u0438\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0446\u0438\u0438 \u041f\u041a \u0441 \u0421\u041a\u041e'), (1015, '\u0421\u0431\u0440\u043e\u0441 \u043a \u0437\u0430\u0432\u043e\u0434\u0441\u043a\u0438\u043c \u043d\u0430\u0441\u0442\u0440\u043e\u0439\u043a\u0430\u043c')])),
                ('result', models.IntegerField(blank=True, null=True, verbose_name='\u0420\u0435\u0437\u0443\u043b\u044c\u0442\u0430\u0442', choices=[(0, '\u041f\u0440\u0438\u043d\u044f\u0442 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u044b\u0439 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442'), (1, '\u041f\u0440\u0438\u043d\u044f\u0442 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442 \u0432\u043d\u0435\u0441\u0435\u043d\u0438\u0435'), (2, '\u041f\u0440\u0438\u043d\u044f\u0442 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442 \u0432\u044b\u0434\u0430\u0447\u0430'), (3, '\u041f\u0440\u0438\u043d\u044f\u0442 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442 \u0432\u043e\u0437\u0432\u0440\u0430\u0442'), (4, '\u041f\u0440\u0438\u043d\u044f\u0442 \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442 \u0430\u043d\u043d\u0443\u043b\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435'), (20, '\u041f\u0440\u0438\u043d\u044f\u0442 Z \u2013 \u043e\u0442\u0447\u0435\u0442'), (555, '\u0422\u0435\u0441\u0442\u0438\u0440\u043e\u0432\u0430\u043d\u0438\u0435 \u0421\u041a\u041d\u041e \u0437\u0430\u0432\u0435\u0440\u0448\u0435\u043d\u043e \u0443\u0441\u043f\u0435\u0448\u043d\u043e')])),
                ('issued_at', models.DateTimeField(verbose_name='\u0414\u0430\u0442\u0430 \u0438 \u0432\u0440\u0435\u043c\u044f \u0441\u043e\u0431\u044b\u0442\u0438\u044f')),
                ('created_at', models.DateTimeField(verbose_name='\u0414\u0430\u0442\u0430, \u0432\u0440\u0435\u043c\u044f \u043f\u0440\u0438\u0445\u043e\u0434\u0430 \u0441\u043e\u0431\u044b\u0442\u0438\u044f')),
                ('passed_at', models.DateTimeField(verbose_name='\u0414\u0430\u0442\u0430, \u0432\u0440\u0435\u043c\u044f \u043f\u0435\u0440\u0435\u0434\u0430\u0447\u0438 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u043e\u0433\u043e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 \u0432 \u0421\u041a\u041d\u041e')),
                ('extra', jsonfield.fields.JSONField(verbose_name='\u0414\u043e\u043f\u043e\u043b\u043d\u0438\u0442\u0435\u043b\u044c\u043d\u043e\u0435 \u0440\u0435\u0437\u0435\u0440\u0432\u043e\u043d\u043e\u0435 \u043f\u043e\u043b\u0435')),
                ('status', models.SmallIntegerField(verbose_name='\u0421\u0442\u0430\u0442\u0443\u0441 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u043e\u0433\u043e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 \u0432 \u0426\u041e\u0414 \u0410\u0418\u0421 \u041a\u041a\u041e')),
                ('status_note', models.TextField(verbose_name='\u041f\u0440\u0438\u043c\u0435\u0447\u0430\u043d\u0438\u0435 \u043e \u0441\u043c\u0435\u043d\u0435 \u0441\u0442\u0430\u0442\u0443\u0441\u0430 \u043f\u043b\u0430\u0442\u0435\u0436\u043d\u043e\u0433\u043e \u0434\u043e\u043a\u0443\u043c\u0435\u043d\u0442\u0430 \u0432 \u0426\u041e\u0414 \u0410\u0418\u0421 \u041a\u041a\u041e')),
                ('data_mode', models.IntegerField(default=0, verbose_name='\u0420\u0435\u0436\u0438\u043c \u043f\u0435\u0440\u0435\u0434\u0430\u0447\u0438 \u0434\u0430\u043d\u043d\u044b\u0445', choices=[(0, 'ON-Line'), (1, '\u041f\u043e \u0441\u043e\u0431\u044b\u0442\u0438\u044e'), (2, 'OFF-Line'), (3, '\u041f\u043e Z-\u043e\u0442\u0447\u0451\u0442\u0443'), (4, '\u041f\u041a')])),
                ('cashbox_id', models.IntegerField(verbose_name='\u0418\u0434\u0435\u043d\u0442\u0438\u0444\u0438\u043a\u0430\u0442\u043e\u0440 \u041a\u041e')),
                ('street', models.CharField(max_length=255, verbose_name='\u0423\u043b\u0438\u0446\u0430', blank=True)),
                ('building', models.CharField(max_length=10, verbose_name='\u0414\u043e\u043c', blank=True)),
                ('unp', models.CharField(unique=True, max_length=9, verbose_name='\u0423\u041d\u041f')),
                ('soato', models.CharField(max_length=10, verbose_name='\u0421\u041e\u0410\u0422\u041e', blank=True)),
            ],
            options={
                'verbose_name': '\u0410\u0432\u0430\u0440\u0438\u0439\u043d\u043e\u0435 \u0441\u043e\u0431\u044b\u0442\u0438\u0435',
                'managed': False,
            },
            bases=(models.Model,),
        ),
    ]
