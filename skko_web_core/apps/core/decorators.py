#coding: utf-8
from __future__ import unicode_literals

import time
from functools import wraps

from django.core.exceptions import PermissionDenied


def permissions(roles, cond=None):
    def decorator(function):
        def wrapper(request, *args, **kwargs):
            if request.account.is_active:
                if isinstance(roles, list):
                    if cond:
                        q = []
                        for r in roles:
                            q.append(request.account.has_perm(r))
                        full_cond = cond.replace('__', '%s') % tuple(q)
                        if not eval(full_cond):
                            raise PermissionDenied
                    else:
                        if not any(request.account.has_perm(r) for r in roles):
                            raise PermissionDenied
                else:
                    if not request.account.has_perm(roles):
                        raise PermissionDenied
            else:
                raise PermissionDenied
            return function(request, *args, **kwargs)
        return wrapper
    return decorator


def update_usertask_status(function):
    """
    Updates task before execution and after execution if UserTask instance
    presents in args or kwargs of the function. If task raises exception, this
    function also updates UserTask instance and throws an exception.
    Use only before @task decorator:
    @celery_app.task
    @update_usertask_status
    def func(some_usertask):
        ...
    """
    @wraps(function)
    def wrapper(*args, **kwargs):
        from skko_web_core.apps.reports.models import UserTask
        kwargs_task = kwargs.get('task')
        task = kwargs_task if isinstance(kwargs_task, UserTask) else None
        if not task:
            tasks = [x for x in args if isinstance(x, UserTask)]
            if tasks:
                task = tasks[0]

        if task:
            task.update_state(status=1)

            try:
                result = function(*args, **kwargs)
            except Exception as e:
                task.update_state(3)
                raise e
            else:
                task.update_state(status=2, file_url=result)
                return result
        else:
            return function(*args, **kwargs)

    return wrapper


def cache_by_time(timeout):
    """Декоратор для кэширования функции с заданным временем жизни кэша."""
    def decorator(func):
        cache = {}
        @wraps(func)
        def wrapped(*args, **kwargs):
            current_time = time.time()
            key = (args, frozenset(kwargs.items()))
            if key in cache:
                value, timestamp = cache[key]
                if current_time - timestamp < timeout:
                    return value
            value = func(*args, **kwargs)
            cache[key] = (value, current_time)
            return value
        return wrapped
    return decorator

