<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>Intro to JavaScript amCharts</title>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<link href="images/style.css" rel="stylesheet" type="text/css">
</head>

<body bgcolor="#FFFFFF">
    <p><img src="images/logo.png" /></p>
    <hr>
    <h2>A very short tutorial for those who don't like reading</h2>
	<ul>
	    <li>Copy <b>amcharts</b> folder to your working dir.</li>
	    <li>Go to <i>samples</i> folder and open one of HTML files in your browser - you should see a chart.</li>
	    <li>View source of the page or open this HTML file with text editor.</li>
	    <li>Try to make some changes.</li>
	    <li>Copy/Paste everything to your html/php/asp/etc file and fix paths to scripts</li>
	    <li>Open your file in browser - you should see the same chart.</li>
	    <li>Now it's time to read all the tutorials and check the examples :)</li>
	</ul>
    <hr>
 	<h2>Documentation</h2>
	<ul>
		<li><a href="http://docs.amcharts.com/3/javascriptstockchart">JavaScript reference</a></li>
	</ul>
    <hr>
	<h2>Tutorials</h2>
	<ul>
		<li>For the latest list of tutorials, <a href="http://www.amcharts.com/tutorials/">visit tutorials page</a>.</li>
	</ul>
	<hr>
	<h2>Examples</h2>
	We think that the best way to start learning amCharts (and any other similar software) is to study examples. Check samples folder - there are plenty of them! In case you prefer working in jsFiddle, visit our web site - all samples have a link to jsFiddle where you can edit and view the result instantly.
    <h2>Support</h2>
	In case you have a question, go to our <a href="http://www.amcharts.com/support">support section</a>. You'll find a lot of information there, and if you won't - you can always ask.
	<hr>
	<h2>Good luck!</h2>
	<hr>
</body>
</html>
