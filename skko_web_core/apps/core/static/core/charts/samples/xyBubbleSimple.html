<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>amCharts examples</title>
        <link rel="stylesheet" href="style.css" type="text/css">
        <script src="../amcharts/amcharts.js" type="text/javascript"></script>
        <script src="../amcharts/xy.js" type="text/javascript"></script>
        
        <script type="text/javascript">
            var chart;

            var chartData = [
                {
                    "x": 10,
                    "y": 14,
                    "value": 59
                },
                {
                    "x": 5,
                    "y": 3,
                    "value": 50
                },
                {
                    "x": -10,
                    "y": -3,
                    "value": 19
                },
                {
                    "x": -6,
                    "y": 5,
                    "value": 65
                },
                {
                    "x": 15,
                    "y": -4,
                    "value": 92
                },
                {
                    "x": 13,
                    "y": 1,
                    "value": 8
                },
                {
                    "x": 1,
                    "y": 6,
                    "value": 35
                }
            ];

            AmCharts.ready(function () {
                // XY Chart
                chart = new AmCharts.AmXYChart();
                chart.pathToImages = "../amcharts/images/";
                chart.dataProvider = chartData;
                chart.startDuration = 1.5;

                // AXES
                // X
                var xAxis = new AmCharts.ValueAxis();
                xAxis.title = "X Axis";
                xAxis.position = "bottom";
                xAxis.autoGridCount = true;
                chart.addValueAxis(xAxis);

                // Y
                var yAxis = new AmCharts.ValueAxis();
                yAxis.title = "Y Axis";
                yAxis.position = "left";
                yAxis.autoGridCount = true;
                chart.addValueAxis(yAxis);

                // GRAPH
                var graph = new AmCharts.AmGraph();
                graph.valueField = "value"; // valueField responsible for the size of a bullet
                graph.xField = "x";
                graph.yField = "y";
                graph.lineAlpha = 0;
                graph.bullet = "bubble";
                graph.balloonText = "x:<b>[[x]]</b> y:<b>[[y]]</b><br>value:<b>[[value]]</b>"
                chart.addGraph(graph);

                // WRITE                                
                chart.write("chartdiv");
            });
        </script>
    </head>
    
    <body>
        <div id="chartdiv" style="width: 600px; height: 400px;"></div>
    </body>

</html>