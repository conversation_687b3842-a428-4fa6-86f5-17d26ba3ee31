<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>amCharts examples</title>
        <link rel="stylesheet" href="style.css" type="text/css">
        <script src="../amcharts/amcharts.js" type="text/javascript"></script>
        <script src="../amcharts/serial.js" type="text/javascript"></script>

        <script type="text/javascript">
            // since v3, chart can accept data in JSON format
            // if your category axis parses dates, you should only
            // set date format of your data (dataDateFormat property of AmSerialChart)
            var chartData = [
                {
                    "date": "2009-10-02",
                    "value": 5
                },
                {
                    "date": "2009-10-03",
                    "value": 15
                },
                {
                    "date": "2009-10-04",
                    "value": 13
                },
                {
                    "date": "2009-10-05",
                    "value": 17
                },
                {
                    "date": "2009-10-06",
                    "value": 15
                },
                {
                    "date": "2009-10-09",
                    "value": 19
                },
                {
                    "date": "2009-10-10",
                    "value": 21
                },
                {
                    "date": "2009-10-11",
                    "value": 20
                },
                {
                    "date": "2009-10-12",
                    "value": 20
                },
                {
                    "date": "2009-10-13",
                    "value": 19
                },
                {
                    "date": "2009-10-16",
                    "value": 25
                },
                {
                    "date": "2009-10-17",
                    "value": 24
                },
                {
                    "date": "2009-10-18",
                    "value": 26
                },
                {
                    "date": "2009-10-19",
                    "value": 27
                },
                {
                    "date": "2009-10-20",
                    "value": 25
                },
                {
                    "date": "2009-10-23",
                    "value": 29
                },
                {
                    "date": "2009-10-24",
                    "value": 28
                },
                {
                    "date": "2009-10-25",
                    "value": 30
                },
                {
                    "date": "2009-10-26",
                    "value": 72
                },
                {
                    "date": "2009-10-27",
                    "value": 43
                },
                {
                    "date": "2009-10-30",
                    "value": 31
                },
                {
                    "date": "2009-11-01",
                    "value": 30
                },
                {
                    "date": "2009-11-02",
                    "value": 29
                },
                {
                    "date": "2009-11-03",
                    "value": 27
                },
                {
                    "date": "2009-11-04",
                    "value": 26
                }
            ];

            createFromToFields();

            // create from/to field values programatically
            function createFromToFields(){
                for(var i = 0; i < chartData.length; i++){
                    var value = chartData[i].value;
                    chartData[i].fromValue = value - value * 0.2;
                    chartData[i].toValue = value + value * 0.2;
                }
            }


            AmCharts.ready(function () {
                var chart = new AmCharts.AmSerialChart();
                chart.dataProvider = chartData;
                chart.pathToImages = "../amcharts/images/";
                chart.categoryField = "date";
                chart.dataDateFormat = "YYYY-MM-DD";

                // AXES
                // category
                var categoryAxis = chart.categoryAxis;
                categoryAxis.parseDates = true; // as our data is date-based, we set parseDates to true
                categoryAxis.minPeriod = "DD"; // our data is daily, so we set minPeriod to DD
                categoryAxis.gridAlpha = 0;
                categoryAxis.tickLength = 0;
                categoryAxis.axisAlpha = 0;

                // value
                var valueAxis = new AmCharts.ValueAxis();
                valueAxis.dashLength = 4;
                valueAxis.axisAlpha = 0;
                chart.addValueAxis(valueAxis);


                // FROM GRAPH
                var fromGraph = new AmCharts.AmGraph();
                fromGraph.type = "line";
                fromGraph.valueField = "fromValue";
                fromGraph.lineAlpha = 0;
                fromGraph.showBalloon = false;
                chart.addGraph(fromGraph);

                // TO GRAPH
                var toGraph = new AmCharts.AmGraph();
                toGraph.type = "line";
                toGraph.valueField = "toValue";
                toGraph.lineAlpha = 0;
                toGraph.fillAlphas = 0.2;
                toGraph.fillToGraph = fromGraph;
                toGraph.showBalloon = false;
                chart.addGraph(toGraph);


                // GRAPH
                var graph = new AmCharts.AmGraph();
                graph.type = "line";
                graph.valueField = "value";
                graph.lineColor = "#000000";
                chart.addGraph(graph);

                // CURSOR
                var chartCursor = new AmCharts.ChartCursor();
                chart.addChartCursor(chartCursor);

                chart.creditsPosition = "top-right";

                // WRITE
                chart.write("chartdiv");
            });
        </script>
    </head>

    <body>
        <div id="chartdiv" style="width:100%; height:400px;"></div>
    </body>

</html>