<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>amCharts examples</title>
        <link rel="stylesheet" href="style.css" type="text/css">
        <script src="../amcharts/amcharts.js" type="text/javascript"></script>
        <script src="../amcharts/gauge.js" type="text/javascript"></script>

        <script type="text/javascript">

            var chart = AmCharts.makeChart("chartdiv", {
                type: "gauge",
                titles: [{
                    "text": "Speedometer",
                    "size": 15
                }],

                axes: [{
                    startValue: 0,
                    axisThickness: 1,
                    endValue: 220,
                    valueInterval: 10,
                    bottomTextYOffset: -20,
                    bottomText: "0 km/h",

                    bands: [{
                            startValue: 0,
                            endValue: 90,
                            color: "#00CC00"
                        },

                        {
                            startValue: 90,
                            endValue: 130,
                            color: "#ffac29"
                        },

                        {
                            startValue: 130,
                            endValue: 220,
                            color: "#ea3838",
                            innerRadius: "95%"
                        }
                    ]
                }],

                arrows: [{}]
            });

            setInterval(randomValue, 2000);

             // set random value
            function randomValue() {
                var value = Math.round(Math.random() * 200);
                chart.arrows[0].setValue(value);
                chart.axes[0].setBottomText(value + " km/h");
            }
        </script>
    </head>

    <body>
        <div id="chartdiv" style="width:500px; height:400px;"></div>
    </body>

</html>