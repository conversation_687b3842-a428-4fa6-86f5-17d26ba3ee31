<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>

    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>amCharts examples</title>
        <link rel="stylesheet" href="style.css" type="text/css">
        <script src="../amcharts/amcharts.js" type="text/javascript"></script>
        <script src="../amcharts/xy.js" type="text/javascript"></script>

        <script type="text/javascript">
            var chart;

            var chartData = [{
                "x": 10,
                "y": 14,
                "errorX": 3,
                "errorY": 4
            }, {
                "x": 5,
                "y": 3,
                "errorX": 1.52,
                "errorY": 6.8
            }, {
                "x": -10,
                "y": 3,
                "errorX": 0.8,
                "errorY": 3.5
            }, {
                "x": -6,
                "y": 5,
                "errorX": 1.2,
                "errorY": 4.2
            }, {
                "x": 11,
                "y": -4,
                "errorX": 2.4,
                "errorY": 3.9
            }, {
                "x": 13,
                "y": 1,
                "errorX": 1.5,
                "errorY": 3.3
            }, {
                "x": 1,
                "y": 6,
                "errorX": 2,
                "errorY": 3.3
            }];

            AmCharts.ready(function() {
                // XY Chart
                chart = new AmCharts.AmXYChart();
                chart.pathToImages = "../amcharts/images/";
                chart.dataProvider = chartData;

                // AXES
                // X
                var xAxis = new AmCharts.ValueAxis();
                xAxis.title = "X Axis";
                xAxis.position = "bottom";
                xAxis.autoGridCount = true;
                chart.addValueAxis(xAxis);

                // Y
                var yAxis = new AmCharts.ValueAxis();
                yAxis.title = "Y Axis";
                yAxis.position = "left";
                yAxis.autoGridCount = true;
                yAxis.minMaxMultiplier = 1.2;
                chart.addValueAxis(yAxis);

                // GRAPH
                var graph = new AmCharts.AmGraph();
                graph.errorField = "errorX"; // valueField responsible for the size of a bullet
                graph.xField = "x";
                graph.yField = "y";
                graph.lineAlpha = 0;
                graph.bulletAxis = xAxis;
                graph.bullet = "xError";
                graph.balloonText = "x:<b>[[x]]</b> y:<b>[[y]]</b><br>x error:<b>[[errorX]]</b><br>y error:<b>[[errorY]]</b>";
                chart.addGraph(graph);

                // GRAPH
                graph = new AmCharts.AmGraph();
                graph.errorField = "errorY"; // valueField responsible for the size of a bullet
                graph.xField = "x";
                graph.yField = "y";
                graph.lineAlpha = 0;
                graph.bulletAxis = yAxis;
                graph.bullet = "yError";
                graph.balloonText = "x:<b>[[x]]</b> y:<b>[[y]]</b><br>x error:<b>[[errorX]]</b><br>y error:<b>[[errorY]]</b>";
                chart.addGraph(graph);

                // WRITE
                chart.write("chartdiv");
            });
        </script>
    </head>

    <body>
        <div id="chartdiv" style="width: 600px; height: 400px;"></div>
    </body>

</html>