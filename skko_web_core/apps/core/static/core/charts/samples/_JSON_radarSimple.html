<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html>
    
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
        <title>amCharts examples</title>
        <link rel="stylesheet" href="style.css" type="text/css">
        <script src="../amcharts/amcharts.js" type="text/javascript"></script>
        <script src="../amcharts/radar.js" type="text/javascript"></script>

        <script type="text/javascript">
            var chart = AmCharts.makeChart("chartdiv", {
                type: "radar",
                dataProvider: [{
                    "country": "Czech Republic",
                        "litres": 156.9
                }, {
                    "country": "Ireland",
                        "litres": 131.1
                }, {
                    "country": "Germany",
                        "litres": 115.8
                }, {
                    "country": "Australia",
                        "litres": 109.9
                }, {
                    "country": "Austria",
                        "litres": 108.3
                }, {
                    "country": "UK",
                        "litres": 99
                }],
            
            
                categoryField: "country",
                startDuration: 2,
            
            
                valueAxes: [{
                    axisAlpha: 0.15,
                    minimum: 0,
                    dashLength: 3,
                    axisTitleOffset: 20,
                    gridCount: 5
                }],
            
                graphs: [{
            
                    valueField: "litres",
            
                    bullet: "round",
                    balloonText: "[[value]] litres of beer per year"
                }]
            
            });
        </script>
    </head>
    
    <body>
        <div id="chartdiv" style="width:600px; height:400px;"></div>
    </body>

</html>