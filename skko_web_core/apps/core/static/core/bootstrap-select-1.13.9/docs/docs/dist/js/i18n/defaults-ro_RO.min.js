/*!
 * Bootstrap-select v1.13.9 (https://developer.snapappointments.com/bootstrap-select)
 *
 * Copyright 2012-2019 SnapAppointments, LLC
 * Licensed under MIT (https://github.com/snapappointments/bootstrap-select/blob/master/LICENSE)
 */

!function(e,t){void 0===e&&void 0!==window&&(e=window),"function"==typeof define&&define.amd?define(["jquery"],function(e){return t(e)}):"object"==typeof module&&module.exports?module.exports=t(require("jquery")):t(e.jQuery)}(this,function(e){e.fn.selectpicker.defaults={doneButtonText:"\xcenchide",noneSelectedText:"Nu a fost selectat nimic",noneResultsText:"Nu exist\u0103 niciun rezultat {0}",countSelectedText:"{0} din {1} selectat(e)",maxOptionsText:["<PERSON>ita a fost atins\u0103 ({n} {var} max)","<PERSON>ita de grup a fost atins\u0103 ({n} {var} max)",["iteme","item"]],selectAllText:"Selecteaz\u0103 toate",deselectAllText:"Deselecteaz\u0103 toate",multipleSeparator:", "}});