[Unit]
Description=uWSGI SKKO Service
After=syslog.target

[Service]
ExecStartPre=mkdir -p /run/uwsgi/
ExecStartPre=chown -R www-data:www-data /run/uwsgi/

ExecStartPre=mkdir -p /var/log/uwsgi/
ExecStartPre=chown -R www-data:www-data /var/log/uwsgi/

ExecStartPre=mkdir -p /var/log/django/
ExecStartPre=chown -R www-data:www-data /var/log/django/

ExecStart=/srv/venv/web_core/bin/uwsgi --ini /etc/uwsgi.ini
ExecReload=/bin/kill -HUP $MAINPID

KillSignal=SIGINT
Restart=always

Type=notify
StandardError=syslog
NotifyAccess=all

LimitNOFILE=infinity
LimitNPROC=infinity
LimitRTPRIO=infinity

[Install]
WantedBy=multi-user.target
